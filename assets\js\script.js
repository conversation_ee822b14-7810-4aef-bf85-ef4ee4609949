'use strict';

/* ========================
   Helper: Toggle class
   ======================== */
const elementToggleFunc = (elem) => elem.classList.toggle("active");

/* ========================
   Sidebar
   ======================== */
const sidebar = document.querySelector("[data-sidebar]");
const sidebarBtn = document.querySelector("[data-sidebar-btn]");
if (sidebarBtn && sidebar) {
  sidebarBtn.addEventListener("click", () => elementToggleFunc(sidebar));
}

/* ========================
   Modal: Shared container for testimonials, gallery & portfolio
   ======================== */
const testimonialModalContainer = document.querySelector('.testimonials-modal')?.closest('[data-modal-container]');
const galleryModalContainer = document.querySelector('.gallery-modal')?.closest('[data-modal-container]');

// Testimonial modal elements
const testimonialModalCloseBtn = testimonialModalContainer?.querySelector("[data-modal-close-btn]");
const testimonialOverlay = testimonialModalContainer?.querySelector("[data-overlay]");
const testimonialModalImg = testimonialModalContainer?.querySelector("[data-modal-img]");
const testimonialModalTitle = testimonialModalContainer?.querySelector("[data-modal-title]");
const testimonialModalText = testimonialModalContainer?.querySelector("[data-modal-text]");
const testimonialModalDate = testimonialModalContainer?.querySelector("[data-modal-date]") || testimonialModalContainer?.querySelector("time");

// Gallery/Portfolio modal elements  
const galleryModalCloseBtn = galleryModalContainer?.querySelector("[data-modal-close-btn]");
const galleryOverlay = galleryModalContainer?.querySelector("[data-overlay]");
const galleryModalImg = galleryModalContainer?.querySelector("[data-modal-img]");
const galleryModalTitle = galleryModalContainer?.querySelector("[data-modal-title]");
const galleryModalText = galleryModalContainer?.querySelector("[data-modal-text]");
const galleryModalCategory = galleryModalContainer?.querySelector("[data-modal-category]");

function openTestimonialModal() {
  if (!testimonialModalContainer) return;
  testimonialModalContainer.classList.add("active");
  testimonialOverlay?.classList.add("active");
}

function closeTestimonialModal() {
  if (!testimonialModalContainer) return;
  testimonialModalContainer.classList.remove("active");
  testimonialOverlay?.classList.remove("active");
}

function openGalleryModal() {
  if (!galleryModalContainer) return;
  galleryModalContainer.classList.add("active");
  galleryOverlay?.classList.add("active");
}

function closeGalleryModal() {
  if (!galleryModalContainer) return;
  galleryModalContainer.classList.remove("active");
  galleryOverlay?.classList.remove("active");
}

// Event listeners for testimonial modal
testimonialModalCloseBtn?.addEventListener("click", closeTestimonialModal);
testimonialOverlay?.addEventListener("click", closeTestimonialModal);

// Event listeners for gallery modal
galleryModalCloseBtn?.addEventListener("click", closeGalleryModal);
galleryOverlay?.addEventListener("click", closeGalleryModal);

// ESC key to close any open modal
document.addEventListener("keydown", (e) => { 
  if (e.key === "Escape") {
    closeTestimonialModal();
    closeGalleryModal();
  }
});

function setModalDateFrom(sourceTimeEl, modalDateEl) {
  if (!modalDateEl) return;
  if (!sourceTimeEl) {
    modalDateEl.removeAttribute("datetime");
    modalDateEl.textContent = "";
    return;
  }
  const iso = sourceTimeEl.getAttribute("datetime") || sourceTimeEl.dataset.date || null;
  if (iso) modalDateEl.setAttribute("datetime", iso);
  modalDateEl.textContent = (sourceTimeEl.textContent || iso || '').trim();
}

/* ========================
   Testimonials
   ======================== */
// Use event delegation for dynamically loaded testimonials
document.addEventListener("click", (e) => {
  const testimonialsItem = e.target.closest("[data-testimonials-item]");
  if (testimonialsItem) {
    const avatarEl = testimonialsItem.querySelector("[data-testimonials-avatar]") || testimonialsItem.querySelector("img");
    const titleEl = testimonialsItem.querySelector("[data-testimonials-title]");
    const textEl = testimonialsItem.querySelector("[data-testimonials-text]");
    const sourceTimeEl = testimonialsItem.querySelector(".testimonials-date") || testimonialsItem.querySelector("time");

    if (avatarEl && testimonialModalImg) {
      testimonialModalImg.src = avatarEl.src;
      testimonialModalImg.alt = avatarEl.alt || titleEl?.textContent.trim() || '';
    }
    if (titleEl && testimonialModalTitle) testimonialModalTitle.textContent = titleEl.textContent.trim();
    if (textEl && testimonialModalText) testimonialModalText.innerHTML = textEl.innerHTML;
    setModalDateFrom(sourceTimeEl, testimonialModalDate);
    openTestimonialModal();
  }
});/* ========================
   Gallery
   ======================== */
const galleryItems = document.querySelectorAll("[data-gallery-modal]");

galleryItems.forEach(item => {
  item.addEventListener("click", (e) => {
    e.preventDefault();
    
    const imgElement = item.querySelector(".gallery-img img");
    if (imgElement && galleryModalImg) { 
      galleryModalImg.src = imgElement.src; 
      galleryModalImg.alt = imgElement.alt; 
    }
    
    const titleElement = item.querySelector(".gallery-item-title");
    if (titleElement && galleryModalTitle) galleryModalTitle.textContent = titleElement.textContent;
    
    const categoryElement = item.querySelector(".gallery-category");
    const descElement = item.querySelector(".gallery-description");
    const detailsElement = item.querySelector(".gallery-details");
    const videoElement = item.querySelector(".gallery-video");
    
    let modalHTML = "";
    
    // Add category badge
    if (categoryElement) modalHTML += `<p class="modal-category">${categoryElement.textContent}</p>`;
    
    // Add description
    if (descElement) modalHTML += `<p>${descElement.innerHTML}</p>`;
    
    // Add video if available (for training videos)
    if (videoElement && videoElement.dataset.video) {
      let videoUrl = videoElement.dataset.video;
      // Add parameters to prevent downloads and hide controls
      if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        // YouTube embed parameters to disable download
        videoUrl += videoUrl.includes('?') ? '&' : '?';
        videoUrl += 'modestbranding=1&rel=0&showinfo=0&controls=1&disablekb=1&iv_load_policy=3';
      } else if (videoUrl.includes('imagekit.io')) {
        // ImageKit parameters
        videoUrl += videoUrl.includes('?') ? '&' : '?';
        videoUrl += 'controls=1&download=0';
      }
      modalHTML += `<div class="modal-video-wrapper">
                      <iframe src="${videoUrl}" 
                              frameborder="0" 
                              allowfullscreen 
                              allow="autoplay; encrypted-media"
                              sandbox="allow-scripts allow-same-origin allow-presentation"
                              controlsList="nodownload nofullscreen noremoteplayback"
                              disablePictureInPicture></iframe>
                    </div>`;
    }
    
    // Add detailed information
    if (detailsElement) {
      modalHTML += `<div class="modal-details">${detailsElement.innerHTML}</div>`;
    }
    
    if (galleryModalText) galleryModalText.innerHTML = modalHTML;
    openGalleryModal();
  });
});

/* ========================
   Portfolio
   ======================== */
const portfolioItems = document.querySelectorAll("[data-portfolio-modal]");

portfolioItems.forEach(item => {
  item.addEventListener("click", (e) => {
    e.preventDefault();
    
    const imgElement = item.querySelector(".project-img img");
    if (imgElement && galleryModalImg) { 
      galleryModalImg.src = imgElement.src; 
      galleryModalImg.alt = imgElement.alt || ''; 
    }
    
    const titleElement = item.querySelector(".project-title");
    if (titleElement && galleryModalTitle) galleryModalTitle.textContent = titleElement.textContent;
    
    // Get category for modal
    const categoryElement = item.querySelector(".project-category");
    
    const descElement = item.querySelector(".project-description");
    const detailsElement = item.querySelector(".project-details");
    const videoElement = item.querySelector(".project-video");
    
    let modalHTML = "";
    
    // Add category badge
    if (categoryElement) modalHTML += `<p class="modal-category">${categoryElement.textContent}</p>`;
    
    // Add description
    if (descElement) modalHTML += `<p>${descElement.innerHTML}</p>`;
    
    // Add video if available
    if (videoElement && videoElement.dataset.video) {
      let videoUrl = videoElement.dataset.video;
      // Add parameters to prevent downloads and hide controls
      if (videoUrl.includes('youtube.com') || videoUrl.includes('youtu.be')) {
        // YouTube embed parameters to disable download
        videoUrl += videoUrl.includes('?') ? '&' : '?';
        videoUrl += 'modestbranding=1&rel=0&showinfo=0&controls=1&disablekb=1&iv_load_policy=3';
      } else if (videoUrl.includes('imagekit.io')) {
        // ImageKit parameters
        videoUrl += videoUrl.includes('?') ? '&' : '?';
        videoUrl += 'controls=1&download=0';
      }
      modalHTML += `<div class="modal-video-wrapper">
                      <iframe src="${videoUrl}" 
                              frameborder="0" 
                              allowfullscreen 
                              allow="autoplay; encrypted-media"
                              sandbox="allow-scripts allow-same-origin allow-presentation"
                              controlsList="nodownload nofullscreen noremoteplayback"
                              disablePictureInPicture></iframe>
                    </div>`;
    }
    
    // Add detailed information
    if (detailsElement) {
      modalHTML += `<div class="modal-details">${detailsElement.innerHTML}</div>`;
    }
    
    if (galleryModalText) galleryModalText.innerHTML = modalHTML;
    openGalleryModal();
  });
});

/* ========================
   Custom select & filters
   ======================== */
const select = document.querySelector("[data-select]");
const selectItems = document.querySelectorAll("[data-select-item]");
const selectValue = document.querySelector("[data-selecct-value]");
const filterBtn = document.querySelectorAll("[data-filter-btn]");

if (select) select.addEventListener("click", () => elementToggleFunc(select));

const filterItems = document.querySelectorAll("[data-filter-item]");
const filterFunc = (selectedValue) => {
  filterItems.forEach(item => {
    if (selectedValue === "all" || selectedValue === item.dataset.category) {
      item.classList.add("active");
    } else {
      item.classList.remove("active");
    }
  });
};
selectItems.forEach(item => {
  item.addEventListener("click", () => {
    // Use data-select-item attribute or convert text to match data-category values
    let selectedValue = item.getAttribute("data-select-item") || item.innerText.toLowerCase();
    
    // Convert common text values to match data-category attributes
    if (selectedValue === "training videos") selectedValue = "training";
    if (selectedValue === "action shots") selectedValue = "action-shots";
    
    if (selectValue) selectValue.innerText = item.innerText;
    elementToggleFunc(select);
    filterFunc(selectedValue);
  });
});
let lastClickedBtn = filterBtn[0];
filterBtn.forEach(btn => {
  btn.addEventListener("click", function () {
    // Use data-filter-btn attribute value instead of innerText
    const selectedValue = this.getAttribute("data-filter-btn") || this.innerText.toLowerCase();
    if (selectValue) selectValue.innerText = this.innerText;
    filterFunc(selectedValue);
    lastClickedBtn.classList.remove("active");
    this.classList.add("active");
    lastClickedBtn = this;
  });
});

// Initialize filters - show all items by default
document.addEventListener('DOMContentLoaded', () => {
  // Show all gallery and portfolio items by default
  filterFunc('all');
});

/* ========================
   Contact form validation
   ======================== */
const form = document.querySelector("[data-form]");
const formInputs = document.querySelectorAll("[data-form-input]");
const formBtn = document.querySelector("[data-form-btn]");
formInputs.forEach(input => {
  input.addEventListener("input", () => {
    if (form && form.checkValidity()) formBtn?.removeAttribute("disabled");
    else formBtn?.setAttribute("disabled", "");
  });
});

/* ========================
   Page navigation
   ======================== */
const navigationLinks = document.querySelectorAll("[data-nav-link]");
const pages = document.querySelectorAll("[data-page]");
navigationLinks.forEach((link, index) => {
  link.addEventListener("click", function () {
    pages.forEach((page, i) => {
      if (this.innerHTML.toLowerCase() === page.dataset.page) {
        page.classList.add("active");
        navigationLinks[i].classList.add("active");
        window.scrollTo(0, 0);
      } else {
        page.classList.remove("active");
        navigationLinks[i].classList.remove("active");
      }
    });
  });
});

/* ========================
   Load data from Supabase
   ======================== */
async function loadSiteData() {
  try {
    // Initialize Supabase
    const supabaseUrl = 'https://quynurxhfcaofhogsakq.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF1eW51cnhoZmNhb2Zob2dzYWtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NzU4MTIsImV4cCI6MjA3MjM1MTgxMn0.sRLqCT6yg6LkdCWmLXogXnm2n1yvJu0ob9PtnsLA400';
    const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

    // Load site settings
    const { data: settings } = await supabase.from('site_settings').select('*').single();
    if (settings) {
      // Update hero
      const nameEl = document.querySelector('.name');
      if (nameEl) nameEl.textContent = settings.hero_name || 'Moeketsi Nolonolo';

      const titleEl = document.querySelector('.title');
      if (titleEl) titleEl.textContent = settings.hero_subtitle || 'Tennis Coach';

      // Update contacts
      const contactLinks = document.querySelectorAll('.contact-link');
      if (contactLinks[0]) { // email
        contactLinks[0].href = `mailto:${settings.contact_email || '<EMAIL>'}`;
        contactLinks[0].textContent = settings.contact_email || '<EMAIL>';
      }
      if (contactLinks[1]) { // phone
        contactLinks[1].href = `tel:${settings.contact_phone || '+27 78 901 3952'}`;
        contactLinks[1].textContent = settings.contact_phone || '+27 78 901 3952';
      }
      // birthday
      const birthdayEl = document.querySelector('.contact-info time');
      if (birthdayEl && settings.birthday) {
        birthdayEl.textContent = new Date(settings.birthday).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });
      }
      if (contactLinks[2]) { // location
        contactLinks[2].textContent = settings.address || '400 Chief Albert Luthuli Street, Pietermaritzburg 3201, South Africa';
      }
    }

    // Load about data
    const { data: about } = await supabase.from('about').select('*').single();
    if (about) {
      // Update about text
      const aboutTextEls = document.querySelectorAll('[data-page="about"] .about-text p');
      if (about.intro_html) {
        const paragraphs = about.intro_html.split('\n\n');
        aboutTextEls.forEach((p, index) => {
          if (paragraphs[index]) p.textContent = paragraphs[index];
        });
      }

      // Update services dynamically
      if (about.services) {
        const services = JSON.parse(about.services);
        const serviceList = document.querySelector('[data-page="about"] .service-list');

        if (serviceList && services.length > 0) {
          // Clear existing services
          serviceList.innerHTML = '';

          // Generate service items dynamically
          services.forEach(service => {
            const serviceItem = document.createElement('li');
            serviceItem.className = 'service-item';

            serviceItem.innerHTML = `
              <div class="service-icon-box">
                <img alt="${service.title} icon" src="${service.icon_url || './assets/images/icon-design.svg'}" width="40"/>
              </div>
              <div class="service-content-box">
                <h4 class="h4 service-item-title">${service.title}</h4>
                <p class="service-item-text">${service.description}</p>
              </div>
            `;

            serviceList.appendChild(serviceItem);
          });
        }
      }
    }

    // Load testimonials dynamically
    const { data: testimonials } = await supabase.from('testimonials').select('*').order('order_index');
    if (testimonials && testimonials.length > 0) {
      const testimonialsList = document.querySelector('[data-page="about"] .testimonials-list');

      if (testimonialsList) {
        // Clear existing testimonials
        testimonialsList.innerHTML = '';

        // Available avatar images
        const avatarImages = [
          './assets/images/avatar-1.png',
          './assets/images/avatar-2.png',
          './assets/images/avatar-3.png',
          './assets/images/avatar-4.png'
        ];

        // Generate testimonial items dynamically
        testimonials.forEach((testimonial, index) => {
          const testimonialItem = document.createElement('li');
          testimonialItem.className = 'testimonials-item';

          // Cycle through available avatars
          const avatarIndex = index % avatarImages.length;
          const avatarSrc = testimonial.photo_url || avatarImages[avatarIndex];

          testimonialItem.innerHTML = `
            <div class="content-card" data-testimonials-item="">
              <figure class="testimonials-avatar-box">
                <img alt="${testimonial.client_name}" data-testimonials-avatar="" src="${avatarSrc}" width="60"/>
              </figure>
              <h4 class="h4 testimonials-item-title" data-testimonials-title="">${testimonial.client_name}</h4>
              <time class="testimonials-date" datetime="${testimonial.date}">${new Date(testimonial.date).toLocaleDateString()}</time>
              <div class="testimonials-text" data-testimonials-text="">
                <p>${testimonial.quote}</p>
              </div>
            </div>
          `;

          testimonialsList.appendChild(testimonialItem);
        });
      }
    }

    // Load portfolio
    const { data: portfolio } = await supabase.from('portfolio_items').select('*').order('order_index');
    const { data: portfolio_categories } = await supabase.from('portfolio_categories').select('*').order('order_index');
    if (portfolio) {
      // Update portfolio items
      renderPortfolioItems(portfolio, portfolio_categories || []);
    }

    // Load gallery
    const { data: gallery } = await supabase.from('gallery_items').select('*').order('order_index');
    if (gallery) {
      // Update gallery items
    }

    // Load resume data
    const { data: certifications } = await supabase.from('education').select('*').order('order_index');
    const { data: experiences } = await supabase.from('experience').select('*').order('order_index');
    const { data: skillss } = await supabase.from('skills').select('*').order('order_index');

    // Render resume sections
    if (certifications) renderCertifications(certifications);
    if (experiences) renderExperience(experiences);
    if (skillss) renderSkills(skillss);

  } catch (error) {
    console.error('Error loading site data:', error);
  }
}

// Render certifications
function renderCertifications(certifications) {
  const certList = document.querySelector('[data-page="resume"] .timeline-list');
  if (!certList) return;

  certList.innerHTML = '';

  certifications.forEach(cert => {
    const li = document.createElement('li');
    li.className = 'timeline-item';

    const title = document.createElement('h4');
    title.className = 'h4 timeline-item-title';
    title.textContent = cert.title || '';

    // Institution (subtitle)
    const institutionP = document.createElement('p');
    institutionP.className = 'subtitle';
    institutionP.textContent = cert.institution || '';

    // Degree / credential (if present)
    const degreeP = document.createElement('p');
    degreeP.className = 'degree';
    degreeP.textContent = cert.degree || '';

    // Date span (show year or range)
    const span = document.createElement('span');
    let dateStr = '';
    if (cert.start_date) {
      const startYear = new Date(cert.start_date).getFullYear();
      dateStr = startYear.toString();
      if (cert.end_date) {
        const endYear = new Date(cert.end_date).getFullYear();
        if (endYear !== startYear) {
          dateStr += ` — ${endYear}`;
        }
      }
    }
    span.textContent = dateStr;

    // Description
    const p = document.createElement('p');
    p.className = 'timeline-text';
    p.textContent = cert.description || '';

    // Build item DOM in a similar order to admin UI
    li.appendChild(title);
    if (institutionP.textContent) li.appendChild(institutionP);
    if (degreeP.textContent) li.appendChild(degreeP);
    if (span.textContent) li.appendChild(span);
    if (p.textContent) li.appendChild(p);

    certList.appendChild(li);
  });
}

// Render experience
function renderExperience(experiences) {
  const expSection = document.querySelectorAll('[data-page="resume"] .timeline')[1]; // second timeline is experience
  if (!expSection) return;

  const expList = expSection.querySelector('.timeline-list');
  if (!expList) return;

  expList.innerHTML = '';

  experiences.forEach(exp => {
    const li = document.createElement('li');
    li.className = 'timeline-item';

    // Title
    const title = document.createElement('h4');
    title.className = 'h4 timeline-item-title';
    title.textContent = exp.title || '';

    // Company (subtitle)
    const companyP = document.createElement('p');
    companyP.className = 'subtitle';
    companyP.textContent = exp.company || '';

    // Position
    const positionP = document.createElement('p');
    positionP.className = 'position';
    positionP.textContent = exp.position || '';

    // Date span
    const span = document.createElement('span');
    let dateStr = '';
    if (exp.start_date) {
      const startYear = new Date(exp.start_date).getFullYear();
      dateStr = startYear.toString();
      if (exp.end_date) {
        const endYear = new Date(exp.end_date).getFullYear();
        if (endYear !== startYear) {
          dateStr += ` — ${endYear}`;
        }
      } else {
        dateStr += ' — Present';
      }
    }
    span.textContent = dateStr;

    // Description
    const p = document.createElement('p');
    p.className = 'timeline-text';
    p.textContent = exp.description || '';

    // Build item DOM in the same order as admin UI
    li.appendChild(title);
    if (companyP.textContent) li.appendChild(companyP);
    if (positionP.textContent) li.appendChild(positionP);
    if (span.textContent) li.appendChild(span);
    if (p.textContent) li.appendChild(p);

    expList.appendChild(li);
  });
}

// Render skills
function renderSkills(skills) {
  const skillsList = document.querySelector('[data-page="resume"] .skills-list');
  if (!skillsList) return;

  skillsList.innerHTML = '';

  skills.forEach(skill => {
    const li = document.createElement('li');
    li.className = 'skills-item';

    const titleWrapper = document.createElement('div');
    titleWrapper.className = 'title-wrapper';

    const h5 = document.createElement('h5');
    h5.className = 'h5';
    h5.textContent = skill.name;

    const dataEl = document.createElement('data');
    if (skill.proficiency_percent > 0) {
      dataEl.value = skill.proficiency_percent;
      dataEl.textContent = skill.proficiency_percent + '%';
    } else if (skill.text_value) {
      dataEl.value = 100; // for progress bar
      dataEl.textContent = skill.text_value;
    }

    titleWrapper.appendChild(h5);
    titleWrapper.appendChild(dataEl);

    const progressBg = document.createElement('div');
    progressBg.className = 'skill-progress-bg';

    const progressFill = document.createElement('div');
    progressFill.className = 'skill-progress-fill';
    progressFill.style.width = (skill.proficiency_percent > 0 ? skill.proficiency_percent : 100) + '%';

    progressBg.appendChild(progressFill);

    li.appendChild(titleWrapper);
    li.appendChild(progressBg);

    skillsList.appendChild(li);
  });
}

// Render portfolio items into the public site
function renderPortfolioItems(items, categories) {
  const projectList = document.querySelector('.project-list');
  if (!projectList) return;

  // Build a map of category slug -> display name
  const catMap = (categories || []).reduce((acc, c) => {
    acc[c.slug] = c.name;
    return acc;
  }, {});

  projectList.innerHTML = '';

  items.forEach(item => {
    const li = document.createElement('li');
    li.className = 'project-item active';
    const categoryName = catMap[item.category_slug] || (item.category_slug || 'Uncategorized');
    // set data-category used by filterFunc (lowercased)
    li.setAttribute('data-category', categoryName.toLowerCase());
    li.setAttribute('data-filter-item', '');

    // Use cover image or first gallery image
    let cover = item.cover_image_url || '';
    if (!cover && item.gallery_urls && Array.isArray(item.gallery_urls) && item.gallery_urls.length > 0) cover = item.gallery_urls[0];

    li.innerHTML = `
      <a href="#" data-portfolio-modal>
        <figure class="project-img">
          <div class="project-item-icon-box">
            <ion-icon name="person-outline"></ion-icon>
          </div>
          <img src="${cover}" alt="${item.title || ''}" loading="lazy" />
        </figure>
        <h3 class="project-title">${item.title || ''}</h3>
        <p class="project-category">${categoryName}</p>
        <p class="project-description" style="display:none;">${item.summary || ''}</p>
        <div class="project-details" style="display:none;">${item.body_html || ''}</div>
      </a>
    `;

    projectList.appendChild(li);
  });

  // Re-bind portfolio modal click handlers (simple approach)
  const portfolioItems = document.querySelectorAll('[data-portfolio-modal]');
  portfolioItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      const imgElement = item.querySelector('.project-img img');
      if (imgElement && galleryModalImg) { 
        galleryModalImg.src = imgElement.src; 
        galleryModalImg.alt = imgElement.alt || ''; 
      }
      const titleElement = item.querySelector('.project-title');
      if (titleElement && galleryModalTitle) galleryModalTitle.textContent = titleElement.textContent;
      const descElement = item.querySelector('.project-description');
      const detailsElement = item.querySelector('.project-details');
      let modalHTML = '';
      if (descElement) modalHTML += `<p>${descElement.innerHTML}</p>`;
      if (detailsElement) modalHTML += `<div class="modal-details">${detailsElement.innerHTML}</div>`;
      if (galleryModalText) galleryModalText.innerHTML = modalHTML;
      openGalleryModal();
    });
  });
}

// Refresh portfolio when admin updates it in another tab
window.addEventListener('storage', (e) => {
  if (e.key === 'portfolio_updated') {
    // reload portfolio data
    loadSiteData().catch(err => console.error('Error reloading site data after portfolio update', err));
  }
});

// Load data on DOMContentLoaded
document.addEventListener('DOMContentLoaded', loadSiteData);
