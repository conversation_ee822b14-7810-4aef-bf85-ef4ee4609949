<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Categories Integration Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="./config.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .category-preview { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; }
        .category-preview h4 { margin: 0 0 10px 0; color: #333; }
        .category-preview .meta { font-size: 12px; color: #666; }
        .portfolio-preview { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin: 10px 0; }
        .portfolio-preview img { width: 100%; height: 150px; object-fit: cover; }
        .portfolio-preview .content { padding: 15px; }
        .portfolio-preview h4 { margin: 0 0 10px 0; color: #333; }
        .portfolio-preview .meta { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio Categories Integration Test</h1>
        
        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="runFullIntegrationTest()">🧪 Run Full Integration Test</button>
            <button onclick="testCategoriesCRUD()">📁 Test Categories CRUD</button>
            <button onclick="testPortfolioWithCategories()">📋 Test Portfolio with Categories</button>
            <button onclick="testCategoryDropdown()">🔽 Test Category Dropdown</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
            <button onclick="cleanupAllTestData()" class="danger">🧽 Cleanup All Test Data</button>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="results"></div>
        </div>
        
        <div class="test-section">
            <h2>Categories Preview</h2>
            <div id="categories-preview"></div>
        </div>
        
        <div class="test-section">
            <h2>Portfolio Items with Categories</h2>
            <div id="portfolio-preview"></div>
        </div>
    </div>
    
    <script>
        const { createClient } = supabase;
        const supabaseClient = createClient(CONFIG.supabase.url, CONFIG.supabase.key);
        let testCategoryId = null;
        let testPortfolioId = null;
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testCategoriesCRUD() {
            try {
                addResult('🧪 Testing Categories CRUD operations...', 'info');
                
                // Test CREATE
                const testCategory = {
                    name: 'Test Category ' + Date.now(),
                    slug: 'test-category-' + Date.now(),
                    order_index: 999
                };
                
                const { data: createdCategory, error: createError } = await supabaseClient
                    .from('portfolio_categories')
                    .insert(testCategory)
                    .select();
                
                if (createError) {
                    addResult(`❌ Category CREATE failed: ${createError.message}`, 'error');
                    return;
                }
                
                testCategoryId = createdCategory[0].id;
                addResult(`✅ Category CREATE successful! ID: ${testCategoryId}`, 'success');
                
                // Test READ
                const { data: categories, error: readError } = await supabaseClient
                    .from('portfolio_categories')
                    .select('*')
                    .order('order_index');
                
                if (readError) {
                    addResult(`❌ Category READ failed: ${readError.message}`, 'error');
                    return;
                }
                
                addResult(`✅ Category READ successful! Found ${categories.length} categories`, 'success');
                
                // Test UPDATE
                const updateData = {
                    name: 'Updated Test Category ' + Date.now(),
                    order_index: 888
                };
                
                const { data: updatedCategory, error: updateError } = await supabaseClient
                    .from('portfolio_categories')
                    .update(updateData)
                    .eq('id', testCategoryId)
                    .select();
                
                if (updateError) {
                    addResult(`❌ Category UPDATE failed: ${updateError.message}`, 'error');
                    return;
                }
                
                addResult(`✅ Category UPDATE successful!`, 'success');
                
                await refreshPreviews();
                
            } catch (error) {
                addResult(`❌ Categories CRUD test error: ${error.message}`, 'error');
            }
        }
        
        async function testPortfolioWithCategories() {
            try {
                addResult('🧪 Testing Portfolio with Categories integration...', 'info');
                
                // First ensure we have a test category
                if (!testCategoryId) {
                    await testCategoriesCRUD();
                }
                
                // Create portfolio item with category
                const testPortfolio = {
                    title: 'Test Portfolio with Category ' + Date.now(),
                    slug: 'test-portfolio-category-' + Date.now(),
                    category_slug: 'test-category-' + Date.now(),
                    summary: 'This portfolio item tests category integration.',
                    body_html: '<p>This is a test portfolio item with <strong>rich text</strong> content.</p>',
                    cover_image_url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop',
                    published: true,
                    order_index: 999
                };
                
                const { data: createdPortfolio, error: createError } = await supabaseClient
                    .from('portfolio_items')
                    .insert(testPortfolio)
                    .select();
                
                if (createError) {
                    addResult(`❌ Portfolio with category CREATE failed: ${createError.message}`, 'error');
                    return;
                }
                
                testPortfolioId = createdPortfolio[0].id;
                addResult(`✅ Portfolio with category CREATE successful!`, 'success');
                
                // Test querying portfolio items with their categories
                const { data: portfolioWithCategories, error: queryError } = await supabaseClient
                    .from('portfolio_items')
                    .select(`
                        *,
                        portfolio_categories!inner(name, slug)
                    `)
                    .eq('id', testPortfolioId);
                
                if (queryError) {
                    addResult(`⚠️ Portfolio-Category join query failed (expected if no matching category): ${queryError.message}`, 'warning');
                } else {
                    addResult(`✅ Portfolio-Category relationship query successful!`, 'success');
                }
                
                await refreshPreviews();
                
            } catch (error) {
                addResult(`❌ Portfolio with categories test error: ${error.message}`, 'error');
            }
        }
        
        async function testCategoryDropdown() {
            try {
                addResult('🧪 Testing Category Dropdown functionality...', 'info');
                
                // Get all categories for dropdown simulation
                const { data: categories, error } = await supabaseClient
                    .from('portfolio_categories')
                    .select('*')
                    .order('order_index');
                
                if (error) {
                    addResult(`❌ Category dropdown data fetch failed: ${error.message}`, 'error');
                    return;
                }
                
                addResult(`✅ Category dropdown data loaded: ${categories.length} categories available`, 'success');
                
                // Simulate dropdown options
                const dropdownOptions = [
                    '<option value="">Select a category...</option>',
                    ...categories.map(cat => `<option value="${cat.slug}">${cat.name}</option>`),
                    '<option value="__add_new__">+ Add New Category</option>'
                ];
                
                addResult(`✅ Dropdown simulation successful with ${dropdownOptions.length} options`, 'success');
                addResult(`<pre>${dropdownOptions.join('\n')}</pre>`, 'info');
                
            } catch (error) {
                addResult(`❌ Category dropdown test error: ${error.message}`, 'error');
            }
        }
        
        async function runFullIntegrationTest() {
            addResult('🚀 Starting full integration test suite...', 'info');
            await testCategoriesCRUD();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testPortfolioWithCategories();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testCategoryDropdown();
            addResult('🏁 Full integration test completed!', 'success');
        }
        
        async function cleanupAllTestData() {
            try {
                addResult('🧪 Cleaning up all test data...', 'info');
                
                // Delete test portfolio items
                const { error: portfolioError } = await supabaseClient
                    .from('portfolio_items')
                    .delete()
                    .like('title', '%Test Portfolio%');
                
                // Delete test categories
                const { error: categoryError } = await supabaseClient
                    .from('portfolio_categories')
                    .delete()
                    .like('name', '%Test Category%');
                
                if (portfolioError || categoryError) {
                    addResult(`⚠️ Cleanup completed with some errors`, 'warning');
                } else {
                    addResult(`✅ All test data cleaned up successfully!`, 'success');
                }
                
                testCategoryId = null;
                testPortfolioId = null;
                await refreshPreviews();
                
            } catch (error) {
                addResult(`❌ Cleanup error: ${error.message}`, 'error');
            }
        }
        
        async function refreshPreviews() {
            try {
                // Refresh categories preview
                const { data: categories, error: catError } = await supabaseClient
                    .from('portfolio_categories')
                    .select('*')
                    .order('order_index')
                    .limit(6);
                
                const categoriesDiv = document.getElementById('categories-preview');
                
                if (catError) {
                    categoriesDiv.innerHTML = `<p class="error">Error loading categories: ${catError.message}</p>`;
                } else if (categories.length === 0) {
                    categoriesDiv.innerHTML = '<p class="info">No categories found.</p>';
                } else {
                    categoriesDiv.innerHTML = `
                        <div class="test-grid">
                            ${categories.map(cat => `
                                <div class="category-preview">
                                    <h4>${cat.name}</h4>
                                    <p><strong>Slug:</strong> ${cat.slug}</p>
                                    <div class="meta">
                                        <strong>Order:</strong> ${cat.order_index}<br>
                                        <strong>Created:</strong> ${new Date(cat.created_at).toLocaleDateString()}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
                
                // Refresh portfolio preview
                const { data: portfolio, error: portError } = await supabaseClient
                    .from('portfolio_items')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(6);
                
                const portfolioDiv = document.getElementById('portfolio-preview');
                
                if (portError) {
                    portfolioDiv.innerHTML = `<p class="error">Error loading portfolio: ${portError.message}</p>`;
                } else if (portfolio.length === 0) {
                    portfolioDiv.innerHTML = '<p class="info">No portfolio items found.</p>';
                } else {
                    portfolioDiv.innerHTML = `
                        <div class="test-grid">
                            ${portfolio.map(item => `
                                <div class="portfolio-preview">
                                    <img src="${item.cover_image_url || 'https://via.placeholder.com/400x150'}" alt="${item.title}">
                                    <div class="content">
                                        <h4>${item.title}</h4>
                                        <p><strong>Category:</strong> ${item.category_slug || 'None'}</p>
                                        <p>${item.summary || 'No summary'}</p>
                                        <div class="meta">
                                            <strong>Status:</strong> ${item.published ? 'Published' : 'Draft'}<br>
                                            <strong>Created:</strong> ${new Date(item.created_at).toLocaleDateString()}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Preview refresh error:', error);
            }
        }
        
        // Auto-load previews on page load
        window.addEventListener('load', () => {
            refreshPreviews();
        });
    </script>
</body>
</html>
