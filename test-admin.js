// Simple test script for admin dashboard debugging
console.log('Test script loaded');

// Test Supabase connection
async function testSupabaseConnection() {
    try {
        console.log('Testing Supabase connection...');

        // Use the same Supabase client as admin.js
        if (window.admin && window.admin.supabase) {
            const supabase = window.admin.supabase;
            console.log('Using admin Supabase client');

            // Test database
            const { data, error } = await supabase.from('portfolio_items').select('*').limit(5);
            console.log('Database test:', { data, error });

            if (error) {
                console.error('Database error:', error);
                alert('Database connection failed: ' + error.message);
            } else {
                console.log('Database connected successfully!');
                alert('Database connected! Found ' + data.length + ' portfolio items.');
            }
        } else {
            // Fallback: create our own client using global Supabase
            console.log('Admin Supabase client not available, creating fallback...');

            const config = window.CONFIG || {
                supabase: {
                    url: 'https://quynurxhfcaofhogsakq.supabase.co',
                    key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF1eW51cnhoZmNhb2Zob2dzYWtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NzU4MTIsImV4cCI6MjA3MjM1MTgxMn0.sRLqCT6yg6LkdCWmLXogXnm2n1yvJu0ob9PtnsLA400'
                }
            };

            // Ensure createClient is available from the global Supabase object
            const createClientFn = window.supabase && window.supabase.createClient;
            if (!createClientFn) {
                throw new Error('Supabase createClient is not available. Make sure the CDN is loaded.');
            }
            const supabase = createClientFn(config.supabase.url, config.supabase.key);

            // Test database
            const { data, error } = await supabase.from('portfolio_items').select('*').limit(5);
            console.log('Database test:', { data, error });

            if (error) {
                console.error('Database error:', error);
                alert('Database connection failed: ' + error.message);
            } else {
                console.log('Database connected successfully!');
                alert('Database connected! Found ' + data.length + ' portfolio items.');
            }
        }

    } catch (err) {
        console.error('Test failed:', err);
        alert('Test failed: ' + err.message);
    }
}

// Test data loading
async function testDataLoading() {
    try {
        console.log('Testing data loading...');

        if (window.admin) {
            console.log('Admin object exists');
            console.log('Admin data:', window.admin.data);

            // Test render methods
            window.admin.renderGallery();
            window.admin.renderPortfolio();
            window.admin.renderMessages();

            alert('Data loading test completed. Check console for details.');
        } else {
            console.error('Admin object not found');
            alert('Admin object not found. Make sure admin.js is loaded.');
        }

    } catch (err) {
        console.error('Data loading test failed:', err);
        alert('Data loading test failed: ' + err.message);
    }
}

// Add test buttons to page
function addTestButtons() {
    const testDiv = document.createElement('div');
    testDiv.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 9999;
        background: white;
        border: 2px solid #238636;
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
        font-size: 12px;
    `;

    testDiv.innerHTML = `
        <h4 style="margin: 0 0 10px 0; color: #238636; font-size: 14px;">Debug Tools</h4>
        <button onclick="window.testSupabaseConnection()" style="margin: 5px; padding: 5px 10px; cursor: pointer;">Test DB</button>
        <button onclick="window.testDataLoading()" style="margin: 5px; padding: 5px 10px; cursor: pointer;">Test Data</button>
        <button onclick="console.log('Current data:', window.admin?.data)" style="margin: 5px; padding: 5px 10px; cursor: pointer;">Log Data</button>
    `;

    document.body.appendChild(testDiv);
}

// Run tests when page loads
document.addEventListener('DOMContentLoaded', () => {
    console.log('Test script initialized');
    addTestButtons();
});

// Make functions global
window.testSupabaseConnection = testSupabaseConnection;
window.testDataLoading = testDataLoading;