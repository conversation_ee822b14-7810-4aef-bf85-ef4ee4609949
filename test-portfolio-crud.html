<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio CRUD Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="./config.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .test-section { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .portfolio-preview { border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin: 10px 0; }
        .portfolio-preview img { width: 100%; height: 200px; object-fit: cover; }
        .portfolio-preview .content { padding: 15px; }
        .portfolio-preview h4 { margin: 0 0 10px 0; color: #333; }
        .portfolio-preview .meta { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio CRUD Operations Test</h1>
        
        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="testCreate()">➕ Test Create</button>
            <button onclick="testRead()">📖 Test Read</button>
            <button onclick="testUpdate()">✏️ Test Update</button>
            <button onclick="testDelete()" class="danger">🗑️ Test Delete</button>
            <button onclick="clearResults()">🧹 Clear Results</button>
            <button onclick="cleanupTestData()" class="danger">🧽 Cleanup Test Data</button>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="results"></div>
        </div>
        
        <div class="test-section">
            <h2>Portfolio Items Preview</h2>
            <div id="portfolio-preview"></div>
        </div>
    </div>
    
    <script>
        const { createClient } = supabase;
        const supabaseClient = createClient(CONFIG.supabase.url, CONFIG.supabase.key);
        let testItemId = null;
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            resultsDiv.appendChild(div);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testCreate() {
            try {
                addResult('🧪 Testing CREATE operation...', 'info');
                
                const testItem = {
                    title: 'Test Portfolio Item ' + Date.now(),
                    slug: 'test-portfolio-' + Date.now(),
                    category_slug: 'test-category',
                    summary: 'This is a test portfolio item created by the CRUD test.',
                    body_html: '<p>This is a detailed description of the test portfolio item.</p><p>It includes multiple paragraphs to test HTML content.</p>',
                    cover_image_url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop',
                    gallery_urls: [
                        'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=800&h=600&fit=crop'
                    ],
                    published: true,
                    order_index: 999,
                    metadata: {
                        test: true,
                        created_by: 'crud_test',
                        timestamp: new Date().toISOString()
                    }
                };
                
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .insert(testItem)
                    .select();
                
                if (error) {
                    addResult(`❌ CREATE failed: ${error.message}`, 'error');
                } else {
                    testItemId = data[0].id;
                    addResult(`✅ CREATE successful! Item ID: ${testItemId}`, 'success');
                    addResult(`<pre>${JSON.stringify(data[0], null, 2)}</pre>`, 'info');
                    await refreshPreview();
                }
            } catch (error) {
                addResult(`❌ CREATE error: ${error.message}`, 'error');
            }
        }
        
        async function testRead() {
            try {
                addResult('🧪 Testing READ operation...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (error) {
                    addResult(`❌ READ failed: ${error.message}`, 'error');
                } else {
                    addResult(`✅ READ successful! Found ${data.length} items`, 'success');
                    if (data.length > 0) {
                        addResult(`Latest item: ${data[0].title}`, 'info');
                    }
                    await refreshPreview();
                }
            } catch (error) {
                addResult(`❌ READ error: ${error.message}`, 'error');
            }
        }
        
        async function testUpdate() {
            try {
                if (!testItemId) {
                    addResult('⚠️ No test item to update. Run CREATE test first.', 'warning');
                    return;
                }
                
                addResult('🧪 Testing UPDATE operation...', 'info');
                
                const updateData = {
                    title: 'Updated Test Portfolio Item ' + Date.now(),
                    summary: 'This item has been updated by the CRUD test.',
                    body_html: '<p>This content has been updated!</p><p>New paragraph added during update.</p>',
                    metadata: {
                        test: true,
                        updated_by: 'crud_test',
                        updated_at: new Date().toISOString()
                    }
                };
                
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .update(updateData)
                    .eq('id', testItemId)
                    .select();
                
                if (error) {
                    addResult(`❌ UPDATE failed: ${error.message}`, 'error');
                } else {
                    addResult(`✅ UPDATE successful!`, 'success');
                    addResult(`<pre>${JSON.stringify(data[0], null, 2)}</pre>`, 'info');
                    await refreshPreview();
                }
            } catch (error) {
                addResult(`❌ UPDATE error: ${error.message}`, 'error');
            }
        }
        
        async function testDelete() {
            try {
                if (!testItemId) {
                    addResult('⚠️ No test item to delete. Run CREATE test first.', 'warning');
                    return;
                }
                
                addResult('🧪 Testing DELETE operation...', 'info');
                
                const { error } = await supabaseClient
                    .from('portfolio_items')
                    .delete()
                    .eq('id', testItemId);
                
                if (error) {
                    addResult(`❌ DELETE failed: ${error.message}`, 'error');
                } else {
                    addResult(`✅ DELETE successful! Removed item ${testItemId}`, 'success');
                    testItemId = null;
                    await refreshPreview();
                }
            } catch (error) {
                addResult(`❌ DELETE error: ${error.message}`, 'error');
            }
        }
        
        async function cleanupTestData() {
            try {
                addResult('🧪 Cleaning up test data...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .delete()
                    .like('title', '%Test Portfolio%');
                
                if (error) {
                    addResult(`❌ Cleanup failed: ${error.message}`, 'error');
                } else {
                    addResult(`✅ Cleanup successful!`, 'success');
                    testItemId = null;
                    await refreshPreview();
                }
            } catch (error) {
                addResult(`❌ Cleanup error: ${error.message}`, 'error');
            }
        }
        
        async function refreshPreview() {
            try {
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(6);
                
                const previewDiv = document.getElementById('portfolio-preview');
                
                if (error) {
                    previewDiv.innerHTML = `<p class="error">Error loading preview: ${error.message}</p>`;
                } else if (data.length === 0) {
                    previewDiv.innerHTML = '<p class="info">No portfolio items found.</p>';
                } else {
                    previewDiv.innerHTML = `
                        <div class="test-grid">
                            ${data.map(item => `
                                <div class="portfolio-preview">
                                    <img src="${item.cover_image_url || 'https://via.placeholder.com/400x200'}" alt="${item.title}">
                                    <div class="content">
                                        <h4>${item.title}</h4>
                                        <p>${item.summary || 'No summary'}</p>
                                        <div class="meta">
                                            <strong>Category:</strong> ${item.category_slug || 'None'}<br>
                                            <strong>Status:</strong> ${item.published ? 'Published' : 'Draft'}<br>
                                            <strong>Created:</strong> ${new Date(item.created_at).toLocaleDateString()}
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('portfolio-preview').innerHTML = `<p class="error">Preview error: ${error.message}</p>`;
            }
        }
        
        async function runAllTests() {
            addResult('🚀 Starting comprehensive CRUD test suite...', 'info');
            await testCreate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testRead();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testUpdate();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await testDelete();
            addResult('🏁 All tests completed!', 'success');
        }
        
        // Auto-load preview on page load
        window.addEventListener('load', () => {
            refreshPreview();
        });
    </script>
</body>
</html>
