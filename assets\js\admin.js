// Admin Dashboard JavaScript
// Supabase is loaded via CDN in HTML

class AdminDashboard {
    constructor() {
        // Load configuration
        const config = window.CONFIG || {
            supabase: {
                url: 'https://quynurxhfcaofhogsakq.supabase.co',
                key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF1eW51cnhoZmNhb2Zob2dzYWtxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY3NzU4MTIsImV4cCI6MjA3MjM1MTgxMn0.sRLqCT6yg6LkdCWmLXogXnm2n1yvJu0ob9PtnsLA400'
            }
        };

        this.supabaseUrl = config.supabase.url;
        this.supabaseKey = config.supabase.key;
        // Ensure createClient is available from the global Supabase object
        const createClientFn = window.supabase && window.supabase.createClient;
        if (!createClientFn) {
            throw new Error('Supabase createClient is not available. Make sure the CDN is loaded.');
        }
        this.supabase = createClientFn(this.supabaseUrl, this.supabaseKey);        this.currentSection = 'dashboard';
        this.currentTab = 'videos';
        this.currentFilter = 'all';
        this.user = null;
        this.data = {
            stats: {
                totalVisits: 1247,
                newMessages: 3,
                galleryItems: 12,
                portfolioProjects: 8
            },
            personalInfo: {},
            aboutText: {},
            services: [],
            testimonials: [],
            gallery: [],
            portfolio: [],
            education: [],
            experience: [],
            skills: [],
            messages: [],
            activity: []
        };
        
        this.init();
    }

    async init() {
        console.log('Initializing admin dashboard...');

        // Check authentication first
        const { data: { user }, error: authError } = await this.supabase.auth.getUser();
        console.log('Auth check result:', { user, authError });

        if (authError) {
            console.error('Auth error:', authError);
            this.showAlert('Authentication error: ' + authError.message, 'error');
            // For debugging, continue without auth
            console.log('Continuing without authentication for debugging...');
        } else if (!user) {
            console.log('No user found, continuing for debugging');
            this.showAlert('No user authenticated. Using demo mode.', 'warning');
        } else {
            console.log('User authenticated:', user);
            this.user = user;
        }

        // Always continue for debugging purposes
        this.setupEventListeners();

        // Load data first, then render everything
        console.log('Loading data...');
        await this.loadData();
        console.log('Loading settings...');
        await this.loadSettings();

        // Now render all sections with loaded data
        console.log('Rendering sections...');
        this.updateStats();
        this.renderActivity();
        this.renderGallery();
        this.renderPortfolio();
        this.renderAbout();
        this.renderResume();
        this.renderMessages();
        this.setupAnalytics();

        console.log('Admin dashboard initialized with data:', this.data);
        console.log('Data keys:', Object.keys(this.data));
        console.log('Portfolio data:', this.data.portfolio);
        console.log('Gallery data:', this.data.gallery);

        // Show success message
        this.showAlert('Admin dashboard loaded successfully!', 'success');

        // Test database connection
        this.testDatabaseConnection();
    }

    async testDatabaseConnection() {
        try {
            console.log('Testing database connection...');
            const { data, error } = await this.supabase.from('portfolio_items').select('*').limit(1);
            if (error) {
                console.error('Database connection test failed:', error);
                this.showAlert('Database connection failed. Check your Supabase configuration.', 'error');
            } else {
                console.log('Database connection successful, sample data:', data);
                this.showAlert('Database connected successfully!', 'success');
            }
        } catch (err) {
            console.error('Database test error:', err);
            this.showAlert('Database test failed.', 'error');
        }
    }

    async showLogin() {
        // Create login form
        const loginHTML = `
            <div class="login-container">
                <div class="login-form">
                    <h2>Admin Login</h2>
                    <form id="login-form">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <button type="submit" class="btn-primary">Login</button>
                    </form>
                    <button id="magic-link-btn" class="btn-secondary">Send Magic Link</button>
                </div>
            </div>
        `;

        document.body.innerHTML = loginHTML;

        // Setup login event listeners
        document.getElementById('login-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');

            const { error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) {
                this.showAlert(error.message, 'error');
            } else {
                location.reload();
            }
        });

        document.getElementById('magic-link-btn').addEventListener('click', async () => {
            const email = document.getElementById('email').value;
            if (!email) {
                this.showAlert('Please enter your email', 'error');
                return;
            }

            const { error } = await this.supabase.auth.signInWithOtp({
                email,
                options: {
                    emailRedirectTo: window.location.origin + '/admin.html'
                }
            });

            if (error) {
                this.showAlert(error.message, 'error');
            } else {
                this.showAlert('Magic link sent to your email!', 'success');
            }
        });
    }

    async loadData() {
        try {
            // Load all data from Supabase
            const [siteSettings, about, education, experience, skills, portfolio, gallery, testimonials, messages] = await Promise.all([
                this.supabase.from('site_settings').select('*').single(),
                this.supabase.from('about').select('*').single(),
                this.supabase.from('education').select('*').order('order_index'),
                this.supabase.from('experience').select('*').order('order_index'),
                this.supabase.from('skills').select('*').order('order_index'),
                this.supabase.from('portfolio_items').select('*').order('order_index'),
                this.supabase.from('gallery_items').select('*').order('order_index'),
                this.supabase.from('testimonials').select('*').order('order_index'),
                this.supabase.from('contact_messages').select('*').order('received_at', { ascending: false })
            ]);

            // Process and store data
            this.data.siteSettings = siteSettings.data || {};

            // Extract personal info from site_settings
            if (siteSettings.data) {
                this.data.personalInfo = {
                    name: siteSettings.data.hero_name || '',
                    title: siteSettings.data.hero_subtitle || '',
                    email: siteSettings.data.contact_email || '',
                    phone: siteSettings.data.contact_phone || '',
                    birthday: siteSettings.data.birthday || '',
                    location: siteSettings.data.address || '',
                    avatar: './assets/images/my-avatar.png' // Default avatar
                };
            } else {
                this.data.personalInfo = {};
            }

            // Process about data
            if (about.data) {
                this.data.about = about.data;
                // Extract about text from intro_html
                if (about.data.intro_html) {
                    // If it's HTML, extract text content from paragraphs
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = about.data.intro_html;
                    const paragraphs = tempDiv.querySelectorAll('p');
                    this.data.aboutText = {
                        paragraph1: paragraphs[0] ? paragraphs[0].textContent : '',
                        paragraph2: paragraphs[1] ? paragraphs[1].textContent : '',
                        paragraph3: paragraphs[2] ? paragraphs[2].textContent : ''
                    };
                } else {
                    this.data.aboutText = {};
                }
                // Parse services JSON if it exists
                if (about.data.services) {
                    this.data.services = typeof about.data.services === 'string'
                        ? JSON.parse(about.data.services)
                        : about.data.services;
                } else {
                    this.data.services = [];
                }

                // Initialize default services if none exist
                if (this.data.services.length === 0) {
                    this.data.services = [
                        {
                            id: 'service-1',
                            title: 'Private Lessons',
                            description: 'One-on-one personalized coaching sessions tailored to your specific needs and skill level.',
                            icon_url: ''
                        },
                        {
                            id: 'service-2',
                            title: 'Group Training',
                            description: 'Dynamic group sessions that build skills while fostering teamwork and competitive spirit.',
                            icon_url: ''
                        },
                        {
                            id: 'service-3',
                            title: 'Technique Analysis',
                            description: 'Detailed video analysis and feedback to improve your strokes and overall game performance.',
                            icon_url: ''
                        },
                        {
                            id: 'service-4',
                            title: 'Match Strategy',
                            description: 'Develop winning game plans and mental toughness for competitive play and tournaments.',
                            icon_url: ''
                        }
                    ];

                    // Save default services to database
                    await this.supabase
                        .from('about')
                        .upsert({
                            id: about.data?.id || undefined,
                            services: JSON.stringify(this.data.services)
                        });
                }
            } else {
                this.data.about = {};
                this.data.aboutText = {};
                this.data.services = [];
            }

            // Process education data
            this.data.education = education.data || [];

            // Process experience data
            this.data.experience = experience.data || [];

            // Process skills data
            this.data.skills = skills.data || [];

            // Store other data
            this.data.portfolio = portfolio.data || [];
            this.data.gallery = gallery.data || [];
            this.data.testimonials = testimonials.data || [];
            this.data.messages = messages.data || [];

            // Update stats
            this.data.stats.newMessages = this.data.messages.filter(m => m.status === 'new').length;
            this.data.stats.galleryItems = this.data.gallery.length;
            this.data.stats.portfolioProjects = this.data.portfolio.length;

            console.log('Data loaded successfully:', this.data);
            this.showAlert('Data loaded successfully', 'success');

        } catch (error) {
            console.error('Error loading data:', error);
            this.showAlert('Error loading data from database. Using default data.', 'error');

            // Set default data if database fails
            this.data.gallery = [];
            this.data.portfolio = [];
            this.data.testimonials = [];
            this.data.messages = [];
            this.data.activity = [];
        }
    }

    async loadSettings() {
        try {
            // Load settings from Supabase
            const { data, error } = await this.supabase.from('site_settings').select('*').single();

            if (error) {
                throw error;
            }

            // Update local settings
            this.data.siteSettings = data;

            // Update document title and favicon
            document.title = data.site_title || 'Admin Dashboard';
            const faviconLink = document.querySelector('link[rel="icon"]');
            if (faviconLink) {
                faviconLink.href = data.favicon_url || '/favicon.ico';
            }

            console.log('Settings loaded successfully:', data);
        } catch (error) {
            console.error('Error loading settings:', error);
            this.showAlert('Error loading settings from database.', 'error');
        }
    }

    updateStats() {
        // Update stats section with current data
        const { totalVisits, newMessages, galleryItems, portfolioProjects } = this.data.stats;

        document.getElementById('total-visits').innerText = totalVisits.toString();
        document.getElementById('new-messages').innerText = newMessages.toString();
        document.getElementById('gallery-items').innerText = galleryItems.toString();
        document.getElementById('portfolio-projects').innerText = portfolioProjects.toString();
    }

    renderActivity() {
        // Render recent activity section
        const activityList = document.getElementById('activity-list');
        activityList.innerHTML = '';

        const activities = this.data.activity;
        if (activities.length === 0) {
            activityList.innerHTML = '<li>No recent activity</li>';
            return;
        }

        activities.forEach(item => {
            const listItem = document.createElement('li');
            listItem.innerText = `${item.action} on ${new Date(item.timestamp).toLocaleString()}`;
            activityList.appendChild(listItem);
        });
    }

    renderGallery() {
        // Render gallery section
        const galleryContainer = document.getElementById('gallery-grid');
        if (!galleryContainer) return;
        galleryContainer.innerHTML = '';

        const galleryItems = this.data.gallery;
        if (galleryItems.length === 0) {
            galleryContainer.innerHTML = '<p>No gallery items found.</p>';
            return;
        }

        galleryItems.forEach(item => {
            const div = document.createElement('div');
            div.className = 'gallery-item';
            div.innerHTML = `
                <img src="${item.image_url}" alt="${item.title}" />
                <div class="gallery-item-info">
                    <h3>${item.title}</h3>
                    <p>${item.description || ''}</p>
                </div>
            `;
            galleryContainer.appendChild(div);
        });
    }

    renderPortfolio() {
        // Render portfolio section
        const portfolioContainer = document.getElementById('portfolio-items-section');
        if (!portfolioContainer) return;
        portfolioContainer.innerHTML = '';

        const portfolioItems = this.data.portfolio;
        if (portfolioItems.length === 0) {
            portfolioContainer.innerHTML = '<p>No portfolio items found.</p>';
            return;
        }

        portfolioItems.forEach(item => {
            const div = document.createElement('div');
            div.className = 'portfolio-item';
            div.innerHTML = `
                <img src="${item.image_url}" alt="${item.title}" />
                <div class="portfolio-item-info">
                    <h3>${item.title}</h3>
                    <p>${item.description || ''}</p>
                </div>
            `;
            portfolioContainer.appendChild(div);
        });
    }

    renderAbout() {
        // Render about section preview
        const aboutContainer = document.getElementById('about-preview');
        if (!aboutContainer) return;
        aboutContainer.innerHTML = '';

        const aboutData = this.data.about;
        if (!aboutData || Object.keys(aboutData).length === 0) {
            aboutContainer.innerHTML = '<p>No about information found.</p>';
            return;
        }

        aboutContainer.innerHTML = `
            <h4>Preview:</h4>
            <div class="about-content">
                ${aboutData.intro_html || '<p>No introduction text found.</p>'}
            </div>
        `;
    }

    renderServices() {
        // Render services section
        const servicesContainer = document.getElementById('services-list');
        if (!servicesContainer) return;
        servicesContainer.innerHTML = '';

        const services = this.data.services;
        if (!services || services.length === 0) {
            servicesContainer.innerHTML = '<p>No services found.</p>';
            return;
        }

        services.forEach(service => {
            const div = document.createElement('div');
            div.className = 'service-item';
            div.innerHTML = `
                <div class="service-header">
                    <h4>${service.title || 'Untitled Service'}</h4>
                    <div class="service-actions">
                        <button class="btn-icon edit-service" data-id="${service.id}">
                            <ion-icon name="pencil-outline"></ion-icon>
                        </button>
                        <button class="btn-icon delete-service" data-id="${service.id}">
                            <ion-icon name="trash-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                <p>${service.description || ''}</p>
                ${service.icon_url ? `<div class="service-icon"><img src="${service.icon_url}" alt="${service.title}" /></div>` : ''}
            `;
            servicesContainer.appendChild(div);
        });

        // Add event listeners for edit and delete buttons
        servicesContainer.addEventListener('click', (e) => {
            const target = e.target.closest('button');
            if (!target) return;

            const serviceId = target.getAttribute('data-id');
            const service = services.find(s => s.id === serviceId);

            if (target.classList.contains('edit-service')) {
                this.showServiceModal(service);
            } else if (target.classList.contains('delete-service')) {
                if (confirm('Are you sure you want to delete this service?')) {
                    this.deleteService(serviceId);
                }
            }
        });
    }

    renderTestimonials() {
        // Render testimonials section
        const testimonialsContainer = document.getElementById('testimonials-list');
        if (!testimonialsContainer) return;
        testimonialsContainer.innerHTML = '';

        const testimonials = this.data.testimonials;
        if (!testimonials || testimonials.length === 0) {
            testimonialsContainer.innerHTML = '<p>No testimonials found.</p>';
            return;
        }

        testimonials.forEach(testimonial => {
            const div = document.createElement('div');
            div.className = 'testimonial-item';
            div.innerHTML = `
                <div class="testimonial-header">
                    <div class="testimonial-info">
                        <strong>${testimonial.client_name || 'Anonymous'}</strong>
                    </div>
                    <div class="testimonial-actions">
                        <button class="btn-icon edit-testimonial" data-id="${testimonial.id}">
                            <ion-icon name="pencil-outline"></ion-icon>
                        </button>
                        <button class="btn-icon delete-testimonial" data-id="${testimonial.id}">
                            <ion-icon name="trash-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                <div class="testimonial-content">
                    <p>"${testimonial.quote || ''}"</p>
                    ${testimonial.date ? `<div class="testimonial-date">${new Date(testimonial.date).toLocaleDateString()}</div>` : ''}
                </div>
            `;
            testimonialsContainer.appendChild(div);
        });
    }

    populatePersonalInfo() {
        const form = document.getElementById('personal-info-form');
        if (!form || !this.data.personalInfo) return;

        Object.keys(this.data.personalInfo).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = this.data.personalInfo[key] || '';
            }
        });
    }

    populateAboutText() {
        const form = document.getElementById('about-text-form');
        if (!form || !this.data.aboutText) return;

        Object.keys(this.data.aboutText).forEach(key => {
            const textarea = form.querySelector(`[name="${key}"]`);
            if (textarea) {
                textarea.value = this.data.aboutText[key] || '';
            }
        });
    }

    renderResume() {
        // Render education section
        this.renderEducation();

        // Render experience section
        this.renderExperience();

        // Render skills section
        this.renderSkills();
    }

    renderEducation() {
        const educationContainer = document.getElementById('education-list');
        if (!educationContainer) return;
        educationContainer.innerHTML = '';

        const education = this.data.education;
        if (!education || education.length === 0) {
            document.getElementById('education-empty').style.display = 'block';
            return;
        }

        document.getElementById('education-empty').style.display = 'none';

        education.forEach(item => {
            const div = document.createElement('div');
            div.className = 'resume-item';
            div.innerHTML = `
                <div class="resume-item-header">
                    <div class="resume-item-info">
                        <h4>${item.title || 'Untitled'}</h4>
                        <p class="subtitle">${item.institution || ''}</p>
                        <p class="degree">${item.degree || ''}</p>
                        <p class="date">
                            ${item.start_date ? new Date(item.start_date).getFullYear() : ''} - ${item.end_date ? new Date(item.end_date).getFullYear() : 'Present'}
                        </p>
                    </div>
                    <div class="resume-item-actions">
                        <button class="btn-icon edit-education" data-id="${item.id}">
                            <ion-icon name="pencil-outline"></ion-icon>
                        </button>
                        <button class="btn-icon delete-education" data-id="${item.id}">
                            <ion-icon name="trash-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                ${item.description ? `<p class="description">${item.description}</p>` : ''}
            `;
            educationContainer.appendChild(div);
        });

        // Add event listeners for edit and delete buttons
        educationContainer.addEventListener('click', (e) => {
            const target = e.target.closest('button');
            if (!target) return;

            const educationId = target.getAttribute('data-id');
            const educationItem = education.find(e => e.id === educationId);

            if (target.classList.contains('edit-education')) {
                this.showEducationModal(educationItem);
            } else if (target.classList.contains('delete-education')) {
                if (confirm('Are you sure you want to delete this education entry?')) {
                    this.deleteEducation(educationId);
                }
            }
        });
    }

    renderExperience() {
        const experienceContainer = document.getElementById('experience-list');
        if (!experienceContainer) return;
        experienceContainer.innerHTML = '';

        const experience = this.data.experience;
        if (!experience || experience.length === 0) {
            document.getElementById('experience-empty').style.display = 'block';
            return;
        }

        document.getElementById('experience-empty').style.display = 'none';

        experience.forEach(item => {
            const div = document.createElement('div');
            div.className = 'resume-item';
            div.innerHTML = `
                <div class="resume-item-header">
                    <div class="resume-item-info">
                        <h4>${item.title || 'Untitled'}</h4>
                        <p class="subtitle">${item.company || ''}</p>
                        <p class="position">${item.position || ''}</p>
                        <p class="date">
                            ${item.start_date ? new Date(item.start_date).getFullYear() : ''} - ${item.end_date ? new Date(item.end_date).getFullYear() : 'Present'}
                        </p>
                    </div>
                    <div class="resume-item-actions">
                        <button class="btn-icon edit-experience" data-id="${item.id}">
                            <ion-icon name="pencil-outline"></ion-icon>
                        </button>
                        <button class="btn-icon delete-experience" data-id="${item.id}">
                            <ion-icon name="trash-outline"></ion-icon>
                        </button>
                    </div>
                </div>
                ${item.description ? `<p class="description">${item.description}</p>` : ''}
            `;
            experienceContainer.appendChild(div);
        });

        // Add event listeners for edit and delete buttons
        experienceContainer.addEventListener('click', (e) => {
            const target = e.target.closest('button');
            if (!target) return;

            const experienceId = target.getAttribute('data-id');
            const experienceItem = experience.find(e => e.id === experienceId);

            if (target.classList.contains('edit-experience')) {
                this.showExperienceModal(experienceItem);
            } else if (target.classList.contains('delete-experience')) {
                if (confirm('Are you sure you want to delete this experience entry?')) {
                    this.deleteExperience(experienceId);
                }
            }
        });
    }

    renderSkills() {
        const skillsContainer = document.getElementById('skills-list');
        if (!skillsContainer) return;
        skillsContainer.innerHTML = '';

        const skills = this.data.skills;
        if (!skills || skills.length === 0) {
            document.getElementById('skills-empty').style.display = 'block';
            return;
        }

        document.getElementById('skills-empty').style.display = 'none';

        skills.forEach(skill => {
            const div = document.createElement('div');
            div.className = 'skill-item';
            const proficiencyText = skill.text_value || `${skill.proficiency_percent || 0}%`;
            const proficiencyWidth = skill.text_value ? 100 : (skill.proficiency_percent || 0);
            div.innerHTML = `
                <div class="skill-header">
                    <div class="skill-info">
                        <h4>${skill.name || 'Untitled Skill'}</h4>
                        <div class="skill-proficiency">
                            <div class="proficiency-bar">
                                <div class="proficiency-fill" style="width: ${proficiencyWidth}%"></div>
                            </div>
                            <span class="proficiency-text">${proficiencyText}</span>
                        </div>
                    </div>
                    <div class="skill-actions">
                        <button class="btn-icon edit-skill" data-id="${skill.id}">
                            <ion-icon name="pencil-outline"></ion-icon>
                        </button>
                        <button class="btn-icon delete-skill" data-id="${skill.id}">
                            <ion-icon name="trash-outline"></ion-icon>
                        </button>
                    </div>
                </div>
            `;
            skillsContainer.appendChild(div);
        });

        // Add event listeners for edit and delete buttons
        skillsContainer.addEventListener('click', (e) => {
            const target = e.target.closest('button');
            if (!target) return;

            const skillId = target.getAttribute('data-id');
            const skillItem = skills.find(s => s.id === skillId);

            if (target.classList.contains('edit-skill')) {
                this.showSkillModal(skillItem);
            } else if (target.classList.contains('delete-skill')) {
                if (confirm('Are you sure you want to delete this skill?')) {
                    this.deleteSkill(skillId);
                }
            }
        });
    }

    createResumeSection(title, items) {
        const sectionDiv = document.createElement('div');
        sectionDiv.className = 'resume-section';
        sectionDiv.innerHTML = `<h3>${title}</h3>`;

        if (!items || items.length === 0) {
            sectionDiv.innerHTML += '<p>No items found.</p>';
            return sectionDiv;
        }

        const list = document.createElement('ul');
        items.forEach(item => {
            const listItem = document.createElement('li');
            listItem.innerHTML = `
                <strong>${item.institution || item.company}</strong> - ${item.degree || item.position}
                <br>
                ${item.start_date ? new Date(item.start_date).toLocaleDateString() : ''} - ${item.end_date ? new Date(item.end_date).toLocaleDateString() : ''}
                <br>
                ${item.description || ''}
            `;
            list.appendChild(listItem);
        });
        sectionDiv.appendChild(list);

        return sectionDiv;
    }

    renderMessages() {
        // Render messages section
        const messagesContainer = document.getElementById('messages-list');
        if (!messagesContainer) return;
        messagesContainer.innerHTML = '';

        const messagesData = this.data.messages;
        if (!messagesData || messagesData.length === 0) {
            messagesContainer.innerHTML = '<p>No messages found.</p>';
            return;
        }

        messagesData.forEach(msg => {
            const div = document.createElement('div');
            div.className = 'message-item';
            div.innerHTML = `
                <strong>${msg.sender_name || 'Unknown'}</strong> <em>${new Date(msg.received_at).toLocaleString()}</em>
                <p>${msg.message}</p>
                <hr>
            `;
            messagesContainer.appendChild(div);
        });
    }

    setupAnalytics() {
        // Initialize analytics (e.g., Google Analytics, Mixpanel, etc.)
        console.log('Setting up analytics...');
        // Example: gtag('config', 'GA_MEASUREMENT_ID');
    }

    showAlert(message, type = 'info') {
        // Show alert messages to the user
        const alertBox = document.createElement('div');
        alertBox.className = `alert alert-${type}`;
        alertBox.innerText = message;

        document.body.appendChild(alertBox);

        // Auto-remove alert after 3 seconds
        setTimeout(() => {
            alertBox.remove();
        }, 3000);
    }

    setupEventListeners() {
        // Setup global event listeners
        document.getElementById('logout-btn')?.addEventListener('click', async () => {
            const { error } = await this.supabase.auth.signOut();
            if (error) {
                this.showAlert('Error signing out: ' + error.message, 'error');
            } else {
                location.reload();
            }
        });

        // Section navigation
        const navLinks = document.querySelectorAll('.nav-link[data-section]');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.getAttribute('data-section');
                this.showSection(section);
            });
        });

        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const tabName = e.target.getAttribute('data-tab');
                this.showTab(tabName);
            });
        });

        // Form submissions
        document.getElementById('save-personal-info')?.addEventListener('click', async () => {
            await this.savePersonalInfo();
        });

        document.getElementById('save-about-text')?.addEventListener('click', async () => {
            await this.saveAboutText();
        });

        // Add buttons
        document.getElementById('add-service-btn')?.addEventListener('click', () => {
            this.showServiceModal();
        });

        document.getElementById('add-testimonial-btn')?.addEventListener('click', () => {
            this.showTestimonialModal();
        });

        document.getElementById('add-education-btn')?.addEventListener('click', () => {
            this.showEducationModal();
        });

        document.getElementById('add-experience-btn')?.addEventListener('click', () => {
            this.showExperienceModal();
        });

        document.getElementById('add-skill-btn')?.addEventListener('click', () => {
            this.showSkillModal();
        });

        // Testimonial actions (edit/delete)
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-testimonial')) {
                e.preventDefault();
                const testimonialId = e.target.closest('.edit-testimonial').getAttribute('data-id');
                const testimonial = this.data.testimonials.find(t => t.id == testimonialId);
                if (testimonial) {
                    this.showTestimonialModal(testimonial);
                }
            }

            if (e.target.closest('.delete-testimonial')) {
                e.preventDefault();
                const testimonialId = e.target.closest('.delete-testimonial').getAttribute('data-id');
                if (confirm('Are you sure you want to delete this testimonial?')) {
                    this.deleteTestimonial(testimonialId);
                }
            }
        });
    }

    showSection(section) {
        // Hide all sections
        document.querySelectorAll('.admin-section').forEach(sec => {
            sec.classList.remove('active');
        });

        // Remove active class from all nav links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Show the selected section
        const targetSection = document.getElementById(section);
        if (targetSection) {
            targetSection.classList.add('active');
        }

        // Add active class to the corresponding nav link
        const targetLink = document.querySelector(`.nav-link[data-section="${section}"]`);
        if (targetLink) {
            targetLink.classList.add('active');
        }

        this.currentSection = section;

        // Load data for the section if not already loaded
        switch (section) {
            case 'dashboard':
                this.updateStats();
                this.renderActivity();
                break;
            case 'gallery':
                this.renderGallery();
                break;
            case 'portfolio':
                this.renderPortfolio();
                break;
            case 'about':
                this.renderAbout();
                this.populatePersonalInfo();
                this.populateAboutText();
                this.renderServices();
                this.renderTestimonials();
                // Show default tab (personal)
                this.showTab('personal');
                break;
            case 'resume':
                this.renderResume();
                break;
            case 'messages':
                this.renderMessages();
                break;
        }
    }

    showTab(tabName) {
        // Handle different tab systems
        if (this.currentSection === 'about') {
            // About section uses about-section class and IDs like personal-section
            document.querySelectorAll('.about-section').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const targetSection = document.getElementById(tabName + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
            }
            const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        } else if (this.currentSection === 'resume') {
            // Resume section uses resume-section class
            document.querySelectorAll('.resume-section').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            const targetSection = document.getElementById(tabName + '-section');
            if (targetSection) {
                targetSection.classList.add('active');
            }
            const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        } else {
            // Other sections use tab-content class
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });

            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Try different ID patterns
            let targetElement = document.getElementById(tabName);
            if (!targetElement) {
                targetElement = document.getElementById(this.currentSection + '-' + tabName + '-section');
            }
            if (targetElement) {
                targetElement.style.display = 'block';
            }
            const targetBtn = document.querySelector(`.tab-btn[data-tab="${tabName}"]`);
            if (targetBtn) {
                targetBtn.classList.add('active');
            }
        }

        this.currentTab = tabName;
    }

    async savePersonalInfo() {
        try {
            const form = document.getElementById('personal-info-form');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Update local data
            this.data.personalInfo = { ...this.data.personalInfo, ...data };

            // First, check if a site_settings row exists
            const { data: existing, error: fetchError } = await this.supabase
                .from('site_settings')
                .select('id')
                .single();

            let result;
            if (existing) {
                // Update existing row
                result = await this.supabase
                    .from('site_settings')
                    .update({
                        hero_name: data.name,
                        hero_subtitle: data.title,
                        contact_email: data.email,
                        contact_phone: data.phone,
                        birthday: data.birthday,
                        address: data.location
                    })
                    .eq('id', existing.id);
            } else {
                // Create new row
                result = await this.supabase
                    .from('site_settings')
                    .insert({
                        hero_name: data.name,
                        hero_subtitle: data.title,
                        contact_email: data.email,
                        contact_phone: data.phone,
                        birthday: data.birthday,
                        address: data.location
                    });
            }

            if (result.error) throw result.error;

            this.showAlert('Personal info saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving personal info:', error);
            this.showAlert('Error saving personal info: ' + error.message, 'error');
        }
    }

    async saveAboutText() {
        try {
            const form = document.getElementById('about-text-form');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Update local data
            this.data.aboutText = { ...this.data.aboutText, ...data };

            // Combine paragraphs into HTML
            const paragraphs = [data.paragraph1, data.paragraph2, data.paragraph3]
                .filter(p => p && p.trim())
                .map(p => `<p>${p.trim()}</p>`)
                .join('');

            // First, check if an about row exists
            const { data: existing, error: fetchError } = await this.supabase
                .from('about')
                .select('id')
                .single();

            // Save to Supabase
            const { error } = await this.supabase
                .from('about')
                .upsert({
                    id: existing?.id || undefined,
                    intro_html: paragraphs
                });

            if (error) throw error;

            this.showAlert('About text saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving about text:', error);
            this.showAlert('Error saving about text: ' + error.message, 'error');
        }
    }

    async saveService(serviceData) {
        try {
            // First, check if an about row exists to store services
            const { data: existing, error: fetchError } = await this.supabase
                .from('about')
                .select('id, services')
                .single();

            let currentServices = [];
            if (existing && existing.services) {
                currentServices = typeof existing.services === 'string'
                    ? JSON.parse(existing.services)
                    : existing.services;
            }

            // Add or update service
            if (serviceData.id) {
                // Update existing service
                const index = currentServices.findIndex(s => s.id === serviceData.id);
                if (index !== -1) {
                    currentServices[index] = serviceData;
                }
            } else {
                // Add new service
                serviceData.id = Date.now().toString(); // Simple ID generation
                currentServices.push(serviceData);
            }

            // Save to database
            const { error } = await this.supabase
                .from('about')
                .upsert({
                    id: existing?.id || undefined,
                    services: JSON.stringify(currentServices)
                });

            if (error) throw error;

            // Update local data
            this.data.services = currentServices;

            // Re-render services
            this.renderServices();

            this.showAlert('Service saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving service:', error);
            this.showAlert('Error saving service: ' + error.message, 'error');
        }
    }

    async deleteService(serviceId) {
        try {
            // First, check if an about row exists to store services
            const { data: existing, error: fetchError } = await this.supabase
                .from('about')
                .select('id, services')
                .single();

            if (fetchError || !existing) {
                throw new Error('No services data found');
            }

            let currentServices = [];
            if (existing.services) {
                currentServices = typeof existing.services === 'string'
                    ? JSON.parse(existing.services)
                    : existing.services;
            }

            // Remove the service
            currentServices = currentServices.filter(s => s.id !== serviceId);

            // Save to database
            const { error } = await this.supabase
                .from('about')
                .update({
                    services: JSON.stringify(currentServices)
                })
                .eq('id', existing.id);

            if (error) throw error;

            // Update local data
            this.data.services = currentServices;

            // Re-render services
            this.renderServices();

            this.showAlert('Service deleted successfully!', 'success');
        } catch (error) {
            console.error('Error deleting service:', error);
            this.showAlert('Error deleting service: ' + error.message, 'error');
        }
    }

    showServiceModal(serviceData = null) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('item-form');

        modalTitle.textContent = serviceData ? 'Edit Service' : 'Add Service';

        // Clear previous form content
        form.innerHTML = '';

        // Create form fields
        form.innerHTML = `
            <div class="form-group">
                <label for="service-title">Service Title</label>
                <input type="text" id="service-title" name="title" value="${serviceData?.title || ''}" required>
            </div>
            <div class="form-group">
                <label for="service-description">Description</label>
                <textarea id="service-description" name="description" rows="4" required>${serviceData?.description || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="service-icon">Icon URL</label>
                <input type="url" id="service-icon" name="icon_url" value="${serviceData?.icon_url || ''}" placeholder="https://...">
                <small class="form-hint">Optional: URL to an icon image for this service</small>
            </div>
            <div class="form-actions">
                <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">Save Service</button>
            </div>
        `;

        // Show modal
        modal.classList.add('active');

        // Handle form submission
        form.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Add ID if editing
            if (serviceData) {
                data.id = serviceData.id;
            }

            await this.saveService(data);
            modal.classList.remove('active');
        };

        // Handle cancel
        document.getElementById('modal-cancel').onclick = () => {
            modal.classList.remove('active');
        };

        // Handle modal close
        document.getElementById('modal-close').onclick = () => {
            modal.classList.remove('active');
        };
    }

    async saveTestimonial(testimonialData) {
        try {
            let result;
            if (testimonialData.id) {
                // Update existing testimonial
                result = await this.supabase
                    .from('testimonials')
                    .update({
                        client_name: testimonialData.client_name,
                        quote: testimonialData.quote,
                        date: testimonialData.date
                    })
                    .eq('id', testimonialData.id);
            } else {
                // Add new testimonial
                result = await this.supabase
                    .from('testimonials')
                    .insert({
                        client_name: testimonialData.client_name,
                        quote: testimonialData.quote,
                        date: testimonialData.date,
                        order_index: this.data.testimonials.length
                    });
            }

            if (result.error) throw result.error;

            // Reload testimonials data
            const { data: testimonials, error: loadError } = await this.supabase
                .from('testimonials')
                .select('*')
                .order('order_index');

            if (!loadError) {
                this.data.testimonials = testimonials || [];
            }

            // Re-render testimonials
            this.renderTestimonials();

            this.showAlert('Testimonial saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving testimonial:', error);
            this.showAlert('Error saving testimonial: ' + error.message, 'error');
        }
    }

    async deleteTestimonial(testimonialId) {
        try {
            const { error } = await this.supabase
                .from('testimonials')
                .delete()
                .eq('id', testimonialId);

            if (error) throw error;

            // Update local data
            this.data.testimonials = this.data.testimonials.filter(t => t.id != testimonialId);

            // Re-render testimonials
            this.renderTestimonials();

            this.showAlert('Testimonial deleted successfully!', 'success');
        } catch (error) {
            console.error('Error deleting testimonial:', error);
            this.showAlert('Error deleting testimonial: ' + error.message, 'error');
        }
    }

    showTestimonialModal(testimonialData = null) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('item-form');

        modalTitle.textContent = testimonialData ? 'Edit Testimonial' : 'Add Testimonial';

        // Clear previous form content
        form.innerHTML = '';

        // Create form fields
        form.innerHTML = `
            <div class="form-group">
                <label for="client-name">Client Name</label>
                <input type="text" id="client-name" name="client_name" value="${testimonialData?.client_name || ''}" required>
            </div>
            <div class="form-group">
                <label for="testimonial-quote">Testimonial Quote</label>
                <textarea id="testimonial-quote" name="quote" rows="4" required>${testimonialData?.quote || ''}</textarea>
            </div>
            <div class="form-group">
                <label for="testimonial-date">Date</label>
                <input type="date" id="testimonial-date" name="date" value="${testimonialData?.date ? new Date(testimonialData.date).toISOString().split('T')[0] : ''}">
            </div>
            <div class="form-actions">
                <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">Save Testimonial</button>
            </div>
        `;

        // Show modal
        modal.classList.add('active');

        // Handle form submission
        form.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Add ID if editing
            if (testimonialData) {
                data.id = testimonialData.id;
            }

            await this.saveTestimonial(data);
            modal.classList.remove('active');
        };

        // Handle cancel
        document.getElementById('modal-cancel').onclick = () => {
            modal.classList.remove('active');
        };

        // Handle modal close
        document.getElementById('modal-close').onclick = () => {
            modal.classList.remove('active');
        };
    }

    // Education CRUD methods
    async saveEducation(educationData) {
        try {
            const { data, error } = await this.supabase
                .from('education')
                .upsert({
                    id: educationData.id || undefined,
                    title: educationData.title,
                    institution: educationData.institution,
                    degree: educationData.degree,
                    start_date: educationData.start_date,
                    end_date: educationData.end_date,
                    description: educationData.description,
                    order_index: educationData.order_index || 0
                })
                .select();

            if (error) throw error;

            // Update local data
            if (educationData.id) {
                // Update existing
                const index = this.data.education.findIndex(e => e.id === educationData.id);
                if (index !== -1) {
                    this.data.education[index] = data[0];
                }
            } else {
                // Add new
                this.data.education.push(data[0]);
            }

            // Re-render education
            this.renderEducation();

            this.showAlert('Education saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving education:', error);
            this.showAlert('Error saving education: ' + error.message, 'error');
        }
    }

    async deleteEducation(educationId) {
        try {
            const { error } = await this.supabase
                .from('education')
                .delete()
                .eq('id', educationId);

            if (error) throw error;

            // Update local data
            this.data.education = this.data.education.filter(e => e.id !== educationId);

            // Re-render education
            this.renderEducation();

            this.showAlert('Education deleted successfully!', 'success');
        } catch (error) {
            console.error('Error deleting education:', error);
            this.showAlert('Error deleting education: ' + error.message, 'error');
        }
    }

    showEducationModal(educationData = null) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('item-form');

        modalTitle.textContent = educationData ? 'Edit Education' : 'Add Education';

        // Clear previous form content
        form.innerHTML = '';

        // Create form fields
        form.innerHTML = `
            <div class="form-group">
                <label for="education-title">Title</label>
                <input type="text" id="education-title" name="title" value="${educationData?.title || ''}" required>
            </div>
            <div class="form-group">
                <label for="education-institution">Institution</label>
                <input type="text" id="education-institution" name="institution" value="${educationData?.institution || ''}" required>
            </div>
            <div class="form-group">
                <label for="education-degree">Degree/Certification</label>
                <input type="text" id="education-degree" name="degree" value="${educationData?.degree || ''}">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="education-start-date">Start Date</label>
                    <input type="date" id="education-start-date" name="start_date" value="${educationData?.start_date ? new Date(educationData.start_date).toISOString().split('T')[0] : ''}">
                </div>
                <div class="form-group">
                    <label for="education-end-date">End Date</label>
                    <input type="date" id="education-end-date" name="end_date" value="${educationData?.end_date ? new Date(educationData.end_date).toISOString().split('T')[0] : ''}">
                </div>
            </div>
            <div class="form-group">
                <label for="education-description">Description</label>
                <textarea id="education-description" name="description" rows="3">${educationData?.description || ''}</textarea>
            </div>
            <div class="form-actions">
                <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">Save Education</button>
            </div>
        `;

        // Show modal
        modal.classList.add('active');

        // Handle form submission
        form.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Add ID if editing
            if (educationData) {
                data.id = educationData.id;
            }

            await this.saveEducation(data);
            modal.classList.remove('active');
        };

        // Handle cancel
        document.getElementById('modal-cancel').onclick = () => {
            modal.classList.remove('active');
        };

        // Handle modal close
        document.getElementById('modal-close').onclick = () => {
            modal.classList.remove('active');
        };
    }

    // Experience CRUD methods
    async saveExperience(experienceData) {
        try {
            const { data, error } = await this.supabase
                .from('experience')
                .upsert({
                    id: experienceData.id || undefined,
                    title: experienceData.title,
                    company: experienceData.company,
                    position: experienceData.position,
                    start_date: experienceData.start_date,
                    end_date: experienceData.end_date,
                    description: experienceData.description,
                    order_index: experienceData.order_index || 0
                })
                .select();

            if (error) throw error;

            // Update local data
            if (experienceData.id) {
                // Update existing
                const index = this.data.experience.findIndex(e => e.id === experienceData.id);
                if (index !== -1) {
                    this.data.experience[index] = data[0];
                }
            } else {
                // Add new
                this.data.experience.push(data[0]);
            }

            // Re-render experience
            this.renderExperience();

            this.showAlert('Experience saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving experience:', error);
            this.showAlert('Error saving experience: ' + error.message, 'error');
        }
    }

    async deleteExperience(experienceId) {
        try {
            const { error } = await this.supabase
                .from('experience')
                .delete()
                .eq('id', experienceId);

            if (error) throw error;

            // Update local data
            this.data.experience = this.data.experience.filter(e => e.id !== experienceId);

            // Re-render experience
            this.renderExperience();

            this.showAlert('Experience deleted successfully!', 'success');
        } catch (error) {
            console.error('Error deleting experience:', error);
            this.showAlert('Error deleting experience: ' + error.message, 'error');
        }
    }

    showExperienceModal(experienceData = null) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('item-form');

        modalTitle.textContent = experienceData ? 'Edit Experience' : 'Add Experience';

        // Clear previous form content
        form.innerHTML = '';

        // Create form fields
        form.innerHTML = `
            <div class="form-group">
                <label for="experience-title">Title</label>
                <input type="text" id="experience-title" name="title" value="${experienceData?.title || ''}" required>
            </div>
            <div class="form-group">
                <label for="experience-company">Company/Organization</label>
                <input type="text" id="experience-company" name="company" value="${experienceData?.company || ''}" required>
            </div>
            <div class="form-group">
                <label for="experience-position">Position</label>
                <input type="text" id="experience-position" name="position" value="${experienceData?.position || ''}">
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="experience-start-date">Start Date</label>
                    <input type="date" id="experience-start-date" name="start_date" value="${experienceData?.start_date ? new Date(experienceData.start_date).toISOString().split('T')[0] : ''}">
                </div>
                <div class="form-group">
                    <label for="experience-end-date">End Date</label>
                    <input type="date" id="experience-end-date" name="end_date" value="${experienceData?.end_date ? new Date(experienceData.end_date).toISOString().split('T')[0] : ''}">
                </div>
            </div>
            <div class="form-group">
                <label for="experience-description">Description</label>
                <textarea id="experience-description" name="description" rows="3">${experienceData?.description || ''}</textarea>
            </div>
            <div class="form-actions">
                <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">Save Experience</button>
            </div>
        `;

        // Show modal
        modal.classList.add('active');

        // Handle form submission
        form.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Add ID if editing
            if (experienceData) {
                data.id = experienceData.id;
            }

            await this.saveExperience(data);
            modal.classList.remove('active');
        };

        // Handle cancel
        document.getElementById('modal-cancel').onclick = () => {
            modal.classList.remove('active');
        };

        // Handle modal close
        document.getElementById('modal-close').onclick = () => {
            modal.classList.remove('active');
        };
    }

    // Skills CRUD methods
    async saveSkill(skillData) {
        try {
            const { data, error } = await this.supabase
                .from('skills')
                .upsert({
                    id: skillData.id || undefined,
                    name: skillData.name,
                    proficiency_percent: parseInt(skillData.proficiency_percent) || 0,
                    text_value: skillData.text_value || null,
                    order_index: skillData.order_index || 0
                })
                .select();

            if (error) throw error;

            // Update local data
            if (skillData.id) {
                // Update existing
                const index = this.data.skills.findIndex(s => s.id === skillData.id);
                if (index !== -1) {
                    this.data.skills[index] = data[0];
                }
            } else {
                // Add new
                this.data.skills.push(data[0]);
            }

            // Re-render skills
            this.renderSkills();

            this.showAlert('Skill saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving skill:', error);
            this.showAlert('Error saving skill: ' + error.message, 'error');
        }
    }

    async deleteSkill(skillId) {
        try {
            const { error } = await this.supabase
                .from('skills')
                .delete()
                .eq('id', skillId);

            if (error) throw error;

            // Update local data
            this.data.skills = this.data.skills.filter(s => s.id !== skillId);

            // Re-render skills
            this.renderSkills();

            this.showAlert('Skill deleted successfully!', 'success');
        } catch (error) {
            console.error('Error deleting skill:', error);
            this.showAlert('Error deleting skill: ' + error.message, 'error');
        }
    }

    showSkillModal(skillData = null) {
        const modal = document.getElementById('modal-overlay');
        const modalTitle = document.getElementById('modal-title');
        const form = document.getElementById('item-form');

        modalTitle.textContent = skillData ? 'Edit Skill' : 'Add Skill';

        // Clear previous form content
        form.innerHTML = '';

        // Create form fields
        form.innerHTML = `
            <div class="form-group">
                <label for="skill-name">Skill Name</label>
                <input type="text" id="skill-name" name="name" value="${skillData?.name || ''}" required>
            </div>
            <div class="form-group">
                <label for="skill-proficiency">Proficiency (%)</label>
                <input type="number" id="skill-proficiency" name="proficiency_percent" min="0" max="100" value="${skillData?.proficiency_percent || 0}">
            </div>
            <div class="form-group">
                <label for="skill-text">Text Value (optional, overrides %)</label>
                <input type="text" id="skill-text" name="text_value" value="${skillData?.text_value || ''}" placeholder="e.g., English & Sesotho">
            </div>
            <div class="form-actions">
                <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                <button type="submit" class="btn-primary">Save Skill</button>
            </div>
        `;

        // Show modal
        modal.classList.add('active');

        // Handle form submission
        form.onsubmit = async (e) => {
            e.preventDefault();
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            // Add ID if editing
            if (skillData) {
                data.id = skillData.id;
            }

            await this.saveSkill(data);
            modal.classList.remove('active');
        };

        // Handle cancel
        document.getElementById('modal-cancel').onclick = () => {
            modal.classList.remove('active');
        };

        // Handle modal close
        document.getElementById('modal-close').onclick = () => {
            modal.classList.remove('active');
        };
    }
}

// Initialize admin dashboard on page load
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});
