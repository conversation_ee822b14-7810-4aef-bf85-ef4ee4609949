<!DOCTYPE html>
<html>
<head>
    <title>Portfolio Modal Test</title>
    <script>
        function testPortfolioModals() {
            // Test if portfolio modal items exist
            const portfolioItems = document.querySelectorAll("[data-portfolio-modal]");
            console.log(`Found ${portfolioItems.length} portfolio items`);
            
            // Test if gallery modal elements exist
            const galleryModal = document.querySelector('.gallery-modal');
            const modalContainer = document.querySelector('[data-modal-container]');
            const modalImg = document.querySelector('[data-modal-img]');
            const modalTitle = document.querySelector('[data-modal-title]');
            const modalText = document.querySelector('[data-modal-text]');
            
            console.log('Gallery modal elements:');
            console.log('- Gallery modal:', !!galleryModal);
            console.log('- Modal container:', !!modalContainer);
            console.log('- Modal image:', !!modalImg);
            console.log('- Modal title:', !!modalTitle);
            console.log('- Modal text:', !!modalText);
            
            // Test clicking first portfolio item
            if (portfolioItems.length > 0) {
                console.log('Testing click on first portfolio item...');
                portfolioItems[0].click();
                
                // Check if modal opened
                setTimeout(() => {
                    const isModalOpen = modalContainer.classList.contains('active');
                    console.log('Modal opened:', isModalOpen);
                    if (isModalOpen) {
                        console.log('SUCCESS: Portfolio modal is working!');
                    } else {
                        console.log('ERROR: Portfolio modal did not open');
                    }
                }, 100);
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testPortfolioModals);
    </script>
</head>
<body>
    <h1>Portfolio Modal Test</h1>
    <p>Check console for test results...</p>
    <iframe src="index.html" width="100%" height="600px"></iframe>
</body>
</html>
