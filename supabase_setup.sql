-- MoJack Tennis Admin Dashboard - Supabase Setup
-- Run these migrations in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Site Settings Table
CREATE TABLE public.site_settings (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    site_title text,
    logo_url text,
    favicon_url text,
    hero_name text,
    hero_subtitle text,
    show_contacts boolean DEFAULT true,
    footer_text text,
    analytics_id text,
    contact_email text,
    contact_phone text,
    whatsapp_link text,
    address text,
    birthday date,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- About Table
CREATE TABLE public.about (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    intro_html text,
    services jsonb,
    cta_text text,
    cta_link text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Resume Table
CREATE TABLE public.resume (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    education jsonb,
    experience jsonb,
    skills jsonb,
    resume_pdf_url text,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Portfolio Categories Table
CREATE TABLE public.portfolio_categories (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text,
    slug text UNIQUE,
    order_index int DEFAULT 0,
    created_at timestamptz DEFAULT now()
);

-- Portfolio Items Table
CREATE TABLE public.portfolio_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text,
    slug text UNIQUE,
    category_slug text,
    summary text,
    body_html text,
    cover_image_url text,
    gallery_urls jsonb,
    published boolean DEFAULT true,
    order_index int DEFAULT 0,
    metadata jsonb,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Gallery Items Table
CREATE TABLE public.gallery_items (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text,
    media_url text,
    media_type text,
    category_slug text,
    caption text,
    alt_text text,
    tags text[],
    order_index int DEFAULT 0,
    published boolean DEFAULT true,
    created_at timestamptz DEFAULT now()
);

-- Testimonials Table
CREATE TABLE public.testimonials (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    client_name text,
    photo_url text,
    quote text,
    date date,
    published boolean DEFAULT true,
    order_index int DEFAULT 0,
    created_at timestamptz DEFAULT now()
);

-- Contact Messages Table
CREATE TABLE public.contact_messages (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text,
    email text,
    phone text,
    message text,
    source text,
    received_at timestamptz DEFAULT now(),
    status text DEFAULT 'new'
);

-- Users Table (mirrored from Supabase Auth)
CREATE TABLE public.users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_uid text,
    email text,
    display_name text,
    role text,
    avatar_url text,
    last_login timestamptz
);

-- Audit Log Table
CREATE TABLE public.audit_log (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    actor_uid text,
    action text,
    target_table text,
    target_id text,
    diff jsonb,
    created_at timestamptz DEFAULT now()
);

-- Education Table
CREATE TABLE public.education (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text,
    institution text,
    degree text,
    start_date date,
    end_date date,
    description text,
    order_index int DEFAULT 0,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Experience Table
CREATE TABLE public.experience (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    title text,
    company text,
    position text,
    start_date date,
    end_date date,
    description text,
    order_index int DEFAULT 0,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Skills Table
CREATE TABLE public.skills (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    name text,
    proficiency_percent int DEFAULT 0,
    text_value text,
    order_index int DEFAULT 0,
    created_at timestamptz DEFAULT now(),
    updated_at timestamptz DEFAULT now()
);

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES
    ('public_media', 'public_media', true),
    ('gallery_videos', 'gallery_videos', true),
    ('resumes', 'resumes', false)
ON CONFLICT (id) DO NOTHING;

-- Row Level Security Policies

-- Site Settings: Only admins can modify
CREATE POLICY "Admins only on site_settings" ON public.site_settings
    FOR ALL USING (auth.role() = 'admin');

-- About: Editors and admins can modify
CREATE POLICY "Editors and Admins on about" ON public.about
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Resume: Editors and admins can modify
CREATE POLICY "Editors and Admins on resume" ON public.resume
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Portfolio Categories: Editors and admins can modify
CREATE POLICY "Editors and Admins on portfolio_categories" ON public.portfolio_categories
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Portfolio Items: Editors and admins can modify
CREATE POLICY "Editors and Admins on portfolio_items" ON public.portfolio_items
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Gallery Items: Editors and admins can modify
CREATE POLICY "Editors and Admins on gallery_items" ON public.gallery_items
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Testimonials: Editors and admins can modify
CREATE POLICY "Editors and Admins on testimonials" ON public.testimonials
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Contact Messages: All authenticated users can read, only admins can modify
CREATE POLICY "Authenticated users can read contact_messages" ON public.contact_messages
    FOR SELECT USING (auth.role() IS NOT NULL);

CREATE POLICY "Admins only modify contact_messages" ON public.contact_messages
    FOR ALL USING (auth.role() = 'admin');

-- Users: Only admins can access
CREATE POLICY "Admins only on users" ON public.users
    FOR ALL USING (auth.role() = 'admin');

-- Audit Log: Only admins can access
CREATE POLICY "Admins only on audit_log" ON public.audit_log
    FOR ALL USING (auth.role() = 'admin');

-- Education: Editors and admins can modify
CREATE POLICY "Editors and Admins on education" ON public.education
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Allow anonymous read access for education
CREATE POLICY "Anonymous read on education" ON public.education
    FOR SELECT USING (true);

-- Experience: Editors and admins can modify
CREATE POLICY "Editors and Admins on experience" ON public.experience
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Allow anonymous read access for experience
CREATE POLICY "Anonymous read on experience" ON public.experience
    FOR SELECT USING (true);

-- Skills: Editors and admins can modify
CREATE POLICY "Editors and Admins on skills" ON public.skills
    FOR ALL USING (auth.role() IN ('admin','editor'));

-- Allow anonymous read access for skills
CREATE POLICY "Anonymous read on skills" ON public.skills
    FOR SELECT USING (true);

-- Storage Policies

-- Public Media Bucket: Public access for reading
CREATE POLICY "Public read access for public_media" ON storage.objects
    FOR SELECT USING (bucket_id = 'public_media');

CREATE POLICY "Authenticated users can upload to public_media" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'public_media' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can update public_media" ON storage.objects
    FOR UPDATE USING (bucket_id = 'public_media' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can delete from public_media" ON storage.objects
    FOR DELETE USING (bucket_id = 'public_media' AND auth.role() IS NOT NULL);

-- Gallery Videos Bucket: Public access for reading
CREATE POLICY "Public read access for gallery_videos" ON storage.objects
    FOR SELECT USING (bucket_id = 'gallery_videos');

CREATE POLICY "Authenticated users can upload to gallery_videos" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'gallery_videos' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can update gallery_videos" ON storage.objects
    FOR UPDATE USING (bucket_id = 'gallery_videos' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can delete from gallery_videos" ON storage.objects
    FOR DELETE USING (bucket_id = 'gallery_videos' AND auth.role() IS NOT NULL);

-- Resumes Bucket: Private access
CREATE POLICY "Authenticated users can read resumes" ON storage.objects
    FOR SELECT USING (bucket_id = 'resumes' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can upload to resumes" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'resumes' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can update resumes" ON storage.objects
    FOR UPDATE USING (bucket_id = 'resumes' AND auth.role() IS NOT NULL);

CREATE POLICY "Authenticated users can delete from resumes" ON storage.objects
    FOR DELETE USING (bucket_id = 'resumes' AND auth.role() IS NOT NULL);

-- Insert default data
INSERT INTO public.site_settings (site_title, hero_name, hero_subtitle, contact_email, contact_phone, whatsapp_link, address, birthday)
VALUES (
    'MoJack - Tennis Coach',
    'MoJack',
    'Tennis Coach',
    '<EMAIL>',
    '+27 78 901 3952',
    'https://wa.me/27789013952',
    '400 Chief Albert Luthuli Street, Pietermaritzburg 3201, South Africa',
    '1982-06-23'
);

INSERT INTO public.about (intro_html, services, cta_text, cta_link)
VALUES (
    '<p>Moeketsi is an accomplished tennis coach with over a decade of experience working with junior and senior players across schools, academies, and clubs in South Africa. Having competed at both regional and national levels as a junior, he transformed his passion for tennis into a professional career focused on inspiring players to achieve their full potential.</p><p>His coaching philosophy embraces a holistic approach that develops athletes technically, tactically, physically, and mentally. Beyond performance on the court, he emphasizes life skills, discipline, and resilience.</p>',
    '[{"id": "1", "title": "Private Lessons", "description": "One-on-one personalized coaching sessions tailored to your specific needs and skill level.", "icon_url": "./assets/images/icon-design.svg"}, {"id": "2", "title": "Group Training", "description": "Dynamic group sessions that build skills while fostering teamwork and competitive spirit.", "icon_url": "./assets/images/icon-dev.svg"}, {"id": "3", "title": "Technique Analysis", "description": "Detailed video analysis and feedback to improve your strokes and overall game performance.", "icon_url": "./assets/images/icon-app.svg"}, {"id": "4", "title": "Match Strategy", "description": "Develop winning game plans and mental toughness for competitive play and tournaments.", "icon_url": "./assets/images/icon-photo.svg"}]',
    'Ready to elevate your tennis game?',
    '#contact'
);

INSERT INTO public.resume (education, experience, skills)
VALUES (
    '[{"title": "Bachelor of Sports Science", "institution": "University of KwaZulu-Natal", "start_year": "2015", "end_year": "2018", "details": "Specialized in sports psychology and athletic performance"}, {"title": "Tennis South Africa (TSA) Operational License", "institution": "Tennis South Africa", "start_year": "2014", "end_year": "2014", "details": ""}, {"title": "SITF National Instructor Level 1", "institution": "International Tennis Federation", "start_year": "2014", "end_year": "2014", "details": ""}]',
    '[{"title": "Current coach at Epworth School", "role": "Tennis Coach", "start_year": "2023", "end_year": "Present", "description": "Leading tennis coaching programs for all skill levels, developing junior players, and managing competitive training sessions at premier tennis facilities."}, {"title": "St Anne''s College", "role": "Tennis Coach", "start_year": "2020", "end_year": "2022", "description": "Leading tennis coaching programs for all skill levels, developing junior players, and managing competitive training sessions at premier tennis facilities."}, {"title": "Cf Tennis-Michael House, Maritzburg college, Cordwalles & St Charles College", "role": "Tennis Coach", "start_year": "2016", "end_year": "2018", "description": "Leading tennis coaching programs for all skill levels, developing junior players, and managing competitive training sessions at premier tennis facilities."}, {"title": "Resident coach at Gowrie Village", "role": "Resident Coach", "start_year": "2021", "end_year": "2021", "description": "Leading tennis coaching programs for all skill levels, developing junior players, and managing competitive training sessions at premier tennis facilities."}, {"title": "uMgungundlovu Tennis Association Development Programme", "role": "Development Coach", "start_year": "2021", "end_year": "2021", "description": "Involved in uMgungundlovu Tennis Association Development Programme for historically disadvantaged youth."}]',
    '[{"name": "Technique Development", "proficiency_percent": 95}, {"name": "Match Strategy", "proficiency_percent": 90}, {"name": "Mental Coaching", "proficiency_percent": 85}, {"name": "Fitness Training", "proficiency_percent": 80}]'
);

-- Insert sample testimonials
INSERT INTO public.testimonials (client_name, quote, date, published, order_index)
VALUES
    ('Thabo Mokoena', 'Working with MoJack has been a game-changer for our junior program. He''s great with kids, mixes fun with discipline, and develops strong habits early. Our players'' technique and match awareness have improved noticeably. A coach who cares about long-term development.', '2025-07-08', true, 1),
    ('Naledi Khumalo', 'As an adult beginner I was nervous about lessons, but MoJack made every session encouraging and effective. He builds confidence while teaching sound fundamentals — I can now rally consistently and feel ready to join local matches. Excellent communicator and completely professional.', '2025-08-01', true, 2),
    ('Ayanda Dlamini', 'MoJack transformed my game in three months. His technique analysis fixed my slice serve, his footwork drills dramatically improved my court coverage, and his match tactics helped me win my first club-level tournament. Clear, patient, and results-focused — I recommend him to any player serious about improving.', '2025-06-18', true, 3),
    ('Pieter van Rensburg', 'I hired MoJack for tournament prep and saw immediate gains. He sharpens strategy, improves mental toughness, and offers practical drills that transfer to match play. His video analysis pinpointed small errors that made a big difference. Highly recommended for competitive players.', '2025-05-02', true, 4);

-- Insert sample gallery items
INSERT INTO public.gallery_items (title, media_url, media_type, category_slug, caption, alt_text, tags, order_index, published)
VALUES
    ('Provincial & SA Junior Team', './assets/images/blog-1.jpg', 'image', 'achievements', 'Selected for KwaZulu-Natal Provincial Teams & SA Junior Team (Perth Tour, 2006).', 'MoJack as junior player representing South Africa', ARRAY['junior', 'provincial', 'national'], 1, true),
    ('League Player Division 1', './assets/images/blog-2.jpg', 'image', 'achievements', 'Competed in Gauteng North Division 1 League (2013–2014).', 'MoJack competing in provincial league tennis', ARRAY['league', 'provincial', 'competition'], 2, true),
    ('Squad Growth – Michael House', './assets/images/blog-3.jpg', 'image', 'achievements', 'Expanded squad sessions from 4 to 12 players.', 'Group of tennis players training', ARRAY['coaching', 'development', 'growth'], 3, true),
    ('Top 20 Junior Progression', './assets/images/blog-4.jpg', 'image', 'achievements', 'Guided a player from intermediate level to national Top 20 (2020–2021).', 'Tennis player celebrating victory', ARRAY['coaching', 'success', 'junior'], 4, true),
    ('Northern Park Primary Tennis', './assets/images/blog-5.jpg', 'image', 'achievements', 'Introduced & established tennis program (2021).', 'Children playing tennis', ARRAY['youth', 'development', 'school'], 5, true),
    ('KZN U13 Boys Coach – Gold', './assets/images/blog-6.jpg', 'image', 'achievements', 'Led KZN U13 Boys to Gold at SA Schools'' Winter Games (2022, 2023).', 'Team celebrating gold medal', ARRAY['coaching', 'gold', 'schools'], 6, true),
    ('BNP Rising Stars Coach', './assets/images/project-9.jpg', 'image', 'achievements', 'Coach – Team Westridge Park Development Hub (BNP Rising Stars).', 'Tennis coaching session', ARRAY['coaching', 'development', 'youth'], 7, true);

-- Insert sample portfolio categories
INSERT INTO public.portfolio_categories (name, slug, order_index)
VALUES
    ('Private Coaching', 'private-coaching', 1),
    ('Group Training', 'group-training', 2),
    ('Junior Development', 'junior-development', 3),
    ('Tournament Prep', 'tournament-prep', 4);

-- Insert sample portfolio items
INSERT INTO public.portfolio_items (title, slug, category_slug, summary, body_html, cover_image_url, gallery_urls, published, order_index)
VALUES
    ('Group & Individual Coaching', 'group-individual-coaching', 'group-training', 'Blended coaching for juniors & adults focusing on fundamentals, tactical awareness, fitness and confidence building.', '<h3>Format:</h3><p>Private 1:1 sessions + small group pods (4–6 players)</p><h3>Focus Areas:</h3><p>Stroke fundamentals, footwork, rally stability, decision making</p><h3>Adults:</h3><p>Technical refinement + match play integration</p><h3>Juniors:</h3><p>Progressive skill blocks & fun competitive drills</p><h3>Benefits:</h3><p>Faster learning curve + motivational group dynamic</p><h3>Booking:</h3><p>Flexible weekly or term-based packages</p>', './assets/images/project-1.jpg', '["./assets/images/project-1.jpg"]', true, 1),
    ('High-Performance & Tournament Prep', 'high-performance-tournament-prep', 'tournament-prep', 'Elite pathway program combining physical, technical, tactical and mental performance layers for competitive players.', '<h3>Components:</h3><p>Advanced patterns, weapon development, pressure drills</p><h3>Match Prep:</h3><p>Scouting, pre-match routines, strategy briefs</p><h3>Post-Match:</h3><p>Data review & adjustment plans</p><h3>Physical:</h3><p>Movement efficiency, recovery protocols</p><h3>Mental:</h3><p>Focus, resilience, momentum management</p><h3>Ideal For:</h3><p>Players targeting ranking improvement & consistency</p>', './assets/images/project-4.jpg', '["./assets/images/project-4.jpg"]', true, 2),
    ('Cardio Tennis Fitness', 'cardio-tennis-fitness', 'group-training', 'High-energy tennis-based fitness sessions blending agility, stroke repetitions and music-driven intensity.', '<h3>Goal:</h3><p>Improve endurance, calorie burn & movement efficiency</p><h3>Format:</h3><p>Circuit drills, live ball rallies, reaction ladders</p><h3>All Levels:</h3><p>Scalable intensity & progression</p><h3>Benefits:</h3><p>Cardio + footwork + repetition without pressure</p><h3>Great For:</h3><p>Players wanting fun conditioning vs gym sessions</p><h3>Schedule:</h3><p>Morning & evening group blocks</p>', './assets/images/project-2.jpg', '["./assets/images/project-2.jpg"]', true, 3),
    ('Long-Term Player Development', 'long-term-player-development', 'junior-development', 'Structured multi-phase pathway guiding juniors from fundamentals to performance maturity.', '<h3>Phases:</h3><p>Fundamentals → Foundation → Training → Performance</p><h3>Monitoring:</h3><p>Seasonal goal reviews & progression metrics</p><h3>Holistic:</h3><p>Technical, tactical, physical, psychological pillars</p><h3>Parent Support:</h3><p>Guidance on competition scheduling & workload</p><h3>Outcome:</h3><p>Sustainable growth & reduced burnout risk</p><h3>Commitment:</h3><p>Quarterly enrollment cycles</p>', './assets/images/project-3.jpg', '["./assets/images/project-3.jpg"]', true, 4),
    ('Video Analysis & Performance Monitoring', 'video-analysis-monitoring', 'private-coaching', 'High-speed stroke recording & data-driven feedback accelerating technical refinement.', '<h3>Reports:</h3><p>Session summaries with priority corrections</p><h3>Tracking:</h3><p>Before/after improvement metrics</p><h3>Application:</h3><p>Serve mechanics, footwork patterns, contact timing</p><h3>Integration:</h3><p>Links directly into live court drills</p><h3>Optional:</h3><p>Periodic progression video archive</p>', './assets/images/project-5.jpg', '["./assets/images/project-5.jpg"]', true, 5),
    ('Talent ID & Mentorship', 'talent-id-mentorship', 'junior-development', 'Personalized guidance for emerging players—bridging raw potential to structured development.', '<h3>Screening:</h3><p>Athletic fundamentals, coordination, learning capacity</p><h3>Mentorship:</h3><p>Goal setting, mindset shaping, routine building</p><h3>Pathway:</h3><p>Competition entry strategy & training balance</p><h3>Support:</h3><p>Parent communication & progress transparency</p><h3>Outcome:</h3><p>Identifies strengths & accelerates focused growth</p><h3>Selection:</h3><p>Invitation-based evaluation blocks</p>', './assets/images/project-6.jpg', '["./assets/images/project-6.jpg"]', true, 6);

-- Insert sample education data (used for certifications)
INSERT INTO public.education (title, institution, degree, start_date, end_date, description, order_index)
VALUES
    ('Tennis South Africa (TSA) Operational License', 'Tennis South Africa', '', '2014-01-01', '2014-12-31', '', 1),
    ('SITF National Instructor Level 1', 'International Tennis Federation', '', '2014-01-01', '2014-12-31', '', 2),
    ('ITF Play Tennis Certificate', 'International Tennis Federation', '', '2014-01-01', '2014-12-31', '', 3),
    ('TSA Officiating Level 1', 'Tennis South Africa', '', '2020-01-01', '2020-12-31', '', 4),
    ('ITF CBI Players Physical conditioning', 'International Tennis Federation', '', '2020-01-01', '2020-12-31', 'Coaching Ethics, Goal Setting, Biomechanics & Movement.', 5),
    ('Continuous professional development with Tennis South Africa', 'Tennis South Africa', '', NULL, NULL, '', 6);

-- Insert sample skills data
INSERT INTO public.skills (name, proficiency_percent, text_value, order_index)
VALUES
    ('Technique Development', 95, NULL, 1),
    ('Match Strategy', 90, NULL, 2),
    ('Mental Coaching', 85, NULL, 3),
    ('Fitness Training', 80, NULL, 4),
    ('Languages', 0, 'English & Sesotho', 5),
    ('Technology', 90, 'MS Office & Social Media', 6),
    ('Leadership', 95, 'Mentorship & Talent Dev', 7);