# MoJack Tennis Admin Dashboard

A comprehensive admin dashboard for managing the MoJack Tennis coaching website. Built with Supabase for backend services, featuring a modern dark UI for content management.

## 🚀 Features

### Core Functionality
- **Authentication**: Secure login with email/password and magic link options
- **Dashboard Overview**: Real-time stats and recent activity
- **Content Management**: Full CRUD operations for all site content
- **Media Library**: Upload and manage images, videos, and documents
- **User Management**: Admin and editor role management
- **SEO Tools**: Meta tags and analytics configuration

### Content Types Managed
- **Site Settings**: Global configuration, contact info, social media
- **About Section**: Personal info, services, testimonials
- **Resume**: Education, experience, skills with drag-and-drop reordering
- **Portfolio**: Projects with categories, rich text content, galleries
- **Gallery**: Image and video management with categories and tags
- **Contact Messages**: Message inbox with reply functionality
- **Testimonials**: Client feedback management

## 🛠️ Tech Stack

- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **UI Framework**: Custom dark theme with responsive design
- **Icons**: Ionicons
- **Styling**: Modern CSS with gradients and animations

## 📋 Prerequisites

- Node.js 16+ (for local development)
- Supabase account and project
- Modern web browser with ES6+ support

## 🚀 Quick Start

### 1. Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Go to SQL Editor and run the `supabase_setup.sql` file
3. Configure storage buckets:
   - `public_media`: Public images and assets
   - `gallery_videos`: Video files for gallery
   - `resumes`: Private resume PDF files

### 2. Authentication Setup

1. In Supabase Dashboard → Authentication → Settings:
   - Configure site URL: `http://localhost:3000` (for development)
   - Enable email confirmations if desired
2. Create admin user accounts via Supabase Auth

### 3. Configuration

Update the Supabase configuration in `config.js`:

```javascript
const CONFIG = {
    supabase: {
        url: 'YOUR_SUPABASE_URL', // Replace with your Supabase project URL
        key: 'YOUR_SUPABASE_ANON_KEY' // Replace with your Supabase anon key
    }
};
```

### 4. Run Locally

```bash
# Serve the files (using any static server)
python -m http.server 3000
# or
npx serve .

# Open http://localhost:3000/admin.html
```

## 📁 Project Structure

```
├── admin.html              # Main admin dashboard UI
├── index.html              # Public portfolio website
├── config.js               # Supabase configuration
├── supabase_setup.sql      # Database schema and initial data
├── assets/
│   ├── css/
│   │   ├── admin.css       # Admin dashboard styles
│   │   └── style.css       # Public site styles
│   └── js/
│       ├── admin.js        # Admin dashboard logic
│       └── script.js       # Public site JavaScript
└── README.md               # This file
```

## 🔐 Authentication & Authorization

### User Roles
- **Admin**: Full access to all features and user management
- **Editor**: Content management (cannot modify users or security settings)

### Row Level Security
All database tables use RLS policies ensuring users can only access authorized data.

## 📊 Dashboard Features

### Overview Widgets
- Total site visits
- New contact messages
- Gallery items count
- Portfolio projects count

### Quick Actions
- Add gallery items
- Create portfolio projects
- View messages
- Edit about section

### Recent Activity Feed
- Content creation/modification tracking
- User action logging

## 🎨 Content Management

### Rich Text Editor
- Bold, italic, links
- Image embedding
- Video embedding
- HTML source editing

### Media Upload
- Drag-and-drop interface
- Progress indicators
- Image previews
- Automatic resizing (client-side)
- Multiple file support

### Drag-and-Drop Reordering
- Skills proficiency ordering
- Service items
- Portfolio categories
- Gallery items

## 📱 Responsive Design

- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly interface
- Collapsible sidebar for mobile

## 🔍 SEO & Analytics

### Meta Tags Management
- Page titles and descriptions
- Open Graph tags
- Twitter Card support

### Analytics Integration
- Google Analytics
- Facebook Pixel
- Custom tracking codes

## 🧪 Testing

### Manual Test Checklist
- [ ] User authentication (login/logout)
- [ ] Content CRUD operations
- [ ] Media upload and management
- [ ] Form validation
- [ ] Responsive layout
- [ ] Cross-browser compatibility

### Automated Tests
Run tests with:
```bash
npm test
```

## 🚀 Deployment

### Production Setup
1. Update Supabase URLs to production endpoints
2. Configure production authentication redirects
3. Set up proper CORS policies
4. Enable CDN for static assets

### Environment Variables
```javascript
// Production config
this.supabaseUrl = 'https://your-project.supabase.co';
this.supabaseKey = 'your-production-anon-key';
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the Supabase documentation
- Review browser console for errors
- Ensure all prerequisites are met

## 🔄 Updates & Maintenance

- Regularly update Supabase client library
- Monitor database performance
- Keep dependencies updated
- Backup database regularly

---

**Built with ❤️ for tennis coaches everywhere**
