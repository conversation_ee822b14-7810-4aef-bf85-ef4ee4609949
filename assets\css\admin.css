/* Admin Dashboard Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Poppins", sans-serif;
    background-color: #0d1117;
    color: #c9d1d9;
    line-height: 1.6;
    overflow-x: hidden;
}

/* Admin Container */
.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.admin-sidebar {
    width: 280px;
    background: linear-gradient(135deg, #161b22 0%, #0d1117 100%);
    border-right: 1px solid #21262d;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid #21262d;
}

.admin-logo {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    filter: brightness(0) saturate(100%) invert(79%) sepia(13%) saturate(493%) hue-rotate(177deg) brightness(97%) contrast(96%);
}

.sidebar-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
}

/* Navigation Styles */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.875rem 1.5rem;
    background: none;
    border: none;
    color: #8b949e;
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: left;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    background-color: #21262d;
    color: #f0f6fc;
}

.nav-link.active {
    background-color: #238636;
    color: #ffffff;
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background-color: #ffffff;
}

.nav-link ion-icon {
    margin-right: 0.75rem;
    font-size: 1.125rem;
}

.nav-link span {
    flex: 1;
}

.badge {
    background-color: #da3633;
    color: #ffffff;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.5rem;
    border-radius: 12px;
    min-width: 1.5rem;
    text-align: center;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid #21262d;
    margin-top: auto;
    position: sticky;
    bottom: 0;
    background: inherit;
}

.back-to-site {
    display: flex;
    align-items: center;
    color: #8b949e;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.2s ease;
}

.back-to-site:hover {
    color: #f0f6fc;
}

.back-to-site ion-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Main Content */
.admin-main {
    flex: 1;
    margin-left: 280px;
    padding: 2rem;
    background-color: #0d1117;
    min-height: 100vh;
}

/* Section Styles */
.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header div:first-child {
    flex: 1;
}

.section-header h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
}

.section-header p {
    color: #8b949e;
    margin: 0.5rem 0 0 0;
    font-size: 1rem;
}

/* Buttons */
.btn-primary, .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-primary {
    background-color: #238636;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #2ea043;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #21262d;
    color: #f0f6fc;
    border: 1px solid #30363d;
}

.btn-secondary:hover {
    background-color: #30363d;
    border-color: #8b949e;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    border-radius: 4px;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    font-family: inherit;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-small ion-icon {
    font-size: 0.875rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.stat-icon {
    background-color: #238636;
    color: #ffffff;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-content h3 {
    font-size: 1.875rem;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
}

.stat-content p {
    color: #8b949e;
    font-size: 0.875rem;
    margin: 0;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
}

.recent-activity, .quick-actions {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
}

.recent-activity h3, .quick-actions h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 1rem;
}

.activity-list {
    list-style: none;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #21262d;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #238636;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    color: #f0f6fc;
    font-size: 0.875rem;
    margin: 0;
}

.activity-content small {
    color: #8b949e;
    font-size: 0.75rem;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
    padding: 0.875rem;
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-family: inherit;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background-color: #30363d;
    border-color: #8b949e;
    transform: translateY(-1px);
}

/* Tabs */
.content-tabs, .resume-tabs {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #21262d;
}

.tab-btn {
    padding: 0.75rem 1.5rem;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: #8b949e;
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tab-btn.active {
    color: #238636;
    border-bottom-color: #238636;
}

.tab-btn:hover:not(.active) {
    color: #f0f6fc;
}

/* Grid Layouts */
.gallery-grid, .portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.gallery-item, .portfolio-item {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.gallery-item:hover, .portfolio-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.item-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background-color: #21262d;
}

.item-content {
    padding: 1.5rem;
}

.item-content h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0 0 0.5rem 0;
}

.item-content p {
    color: #8b949e;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0 0 1rem 0;
}

.item-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.item-meta {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.category {
    background-color: #238636;
    color: #ffffff;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.media-type, .status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.media-type {
    background-color: #1f6feb;
    color: #ffffff;
}

.status.published {
    background-color: #238636;
    color: #ffffff;
}

.status.draft {
    background-color: #656d76;
    color: #ffffff;
}

.btn-edit {
    background-color: #1f6feb;
    color: #ffffff;
    border: none;
}

.btn-edit:hover {
    background-color: #388bfd;
}

.btn-delete {
    background-color: #da3633;
    color: #ffffff;
    border: none;
}

.btn-delete:hover {
    background-color: #f85149;
}

/* Resume Sections */
.resume-section {
    display: none;
}

.resume-section.active {
    display: block;
}

.education-list, .experience-list, .skills-list {
    display: grid;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.resume-item {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.resume-item:hover {
    border-color: #388bfd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(56, 139, 253, 0.1);
}

.resume-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.resume-item h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f0f6fc;
    margin: 0;
    flex: 1;
}

.resume-item .subtitle {
    color: #238636;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.resume-item .degree,
.resume-item .position {
    color: #8b949e;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.resume-item .date {
    color: #8b949e;
    font-size: 0.75rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.resume-item .description {
    color: #8b949e;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

/* Skills specific styles */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
}

.skill-item {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
}

.skill-item:hover {
    border-color: #388bfd;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(56, 139, 253, 0.1);
}

.skill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.skill-name {
    font-weight: 600;
    color: #f0f6fc;
    font-size: 0.875rem;
}

.skill-percentage {
    color: #238636;
    font-size: 0.75rem;
    font-weight: 600;
}

.skill-progress-container {
    background: #21262d;
    height: 6px;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.skill-progress {
    background: linear-gradient(90deg, #238636, #2ea043);
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Reorder mode styles */
.skills-list.reorder-mode .skill-item {
    cursor: move;
    border-color: #f78166;
}

.skills-list.reorder-mode .skill-item.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

/* About text management */
.about-preview-content {
    background: #f6f8fa;
    color: #24292f;
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid #d0d7de;
}

.about-preview-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.about-preview-content p:last-child {
    margin-bottom: 0;
}

.form-hint {
    color: #8b949e;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: block;
}

/* Skills actions */
.skills-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #8b949e;
}

.empty-state ion-icon {
    font-size: 3rem;
    color: #656d76;
    margin-bottom: 1rem;
}

.empty-state p {
    font-size: 1rem;
    margin-bottom: 1.5rem;
}

/* Tab buttons with icons */
.resume-tabs .tab-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
}

.resume-tabs .tab-btn ion-icon {
    font-size: 1rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .skills-grid {
        grid-template-columns: 1fr;
    }
    
    .resume-item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .skills-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .about-tabs {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .about-tabs .tab-btn {
        flex: 1;
        min-width: calc(50% - 0.125rem);
    }
}

.resume-item p {
    color: #8b949e;
    font-size: 0.875rem;
    line-height: 1.5;
    margin: 0;
}

.resume-item .item-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.resume-item:hover .item-actions {
    opacity: 1;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.skill-item {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.skill-name {
    font-weight: 500;
    color: #f0f6fc;
    margin-bottom: 0.5rem;
}

.skill-level {
    width: 100%;
    height: 6px;
    background-color: #21262d;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.skill-progress {
    height: 100%;
    background-color: #238636;
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

/* Messages */
.message-filters {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: none;
    border: 1px solid #30363d;
    border-radius: 20px;
    color: #8b949e;
    font-family: inherit;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-btn.active {
    background-color: #238636;
    border-color: #238636;
    color: #ffffff;
}

.filter-btn:hover:not(.active) {
    border-color: #8b949e;
    color: #f0f6fc;
}

.messages-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.message-item {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.message-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.message-item.unread {
    border-left: 3px solid #238636;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.message-sender {
    font-weight: 600;
    color: #f0f6fc;
}

.message-date {
    color: #8b949e;
    font-size: 0.75rem;
}

.message-subject {
    font-weight: 500;
    color: #238636;
    margin-bottom: 0.5rem;
}

.message-preview {
    color: #8b949e;
    font-size: 0.875rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.message-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    font-size: 0.625rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

.status-unread {
    background-color: #238636;
    color: #ffffff;
}

.status-replied {
    background-color: #1f6feb;
    color: #ffffff;
}

/* Analytics */
.date-range-select {
    padding: 0.5rem 1rem;
    background-color: #21262d;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-family: inherit;
    font-size: 0.875rem;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.chart-card {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
}

.chart-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 1rem;
}

.chart-card canvas {
    max-width: 100%;
    height: 200px;
    border-radius: 4px;
    background: linear-gradient(135deg, #21262d 0%, #30363d 100%);
}

.popular-pages {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.page-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: #21262d;
    border-radius: 6px;
}

.page-name {
    color: #f0f6fc;
    font-size: 0.875rem;
}

.page-views {
    color: #238636;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    padding: 1.5rem;
}

.settings-card h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #f0f6fc;
    margin-bottom: 1.5rem;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    color: #f0f6fc;
    font-size: 0.875rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    background-color: #0d1117;
    border: 1px solid #30363d;
    border-radius: 6px;
    color: #f0f6fc;
    font-family: inherit;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #238636;
}

.form-group input:invalid,
.form-group textarea:invalid {
    border-color: #da3633;
}

.form-group input:valid,
.form-group textarea:valid {
    border-color: #238636;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

/* Modals */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

.message-modal {
    max-width: 700px;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #21262d;
}

.modal-header h3 {
    color: #f0f6fc;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #8b949e;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.modal-close:hover {
    color: #f0f6fc;
}

.modal-content {
    padding: 1.5rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

/* Message Modal Specific */
.message-details {
    margin-bottom: 2rem;
}

.message-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #21262d;
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.meta-label {
    color: #8b949e;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.meta-value {
    color: #f0f6fc;
    font-size: 0.875rem;
}

.message-body {
    background-color: #0d1117;
    border: 1px solid #21262d;
    border-radius: 6px;
    padding: 1rem;
    color: #f0f6fc;
    line-height: 1.6;
    white-space: pre-wrap;
    font-size: 0.875rem;
}

.reply-section {
    border-top: 1px solid #21262d;
    padding-top: 2rem;
}

.reply-section h4 {
    color: #f0f6fc;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* About Management Styles */
.about-management {
    max-width: 100%;
}

.about-tabs {
    display: flex;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-gradient-onyx);
}

.about-tabs .tab-btn {
    padding: 1rem 1.5rem;
    background: none;
    border: none;
    color: var(--light-gray);
    font-size: var(--fs-6);
    cursor: pointer;
    transition: var(--transition-1);
    border-bottom: 2px solid transparent;
}

.about-tabs .tab-btn:hover {
    color: var(--neon-green);
}

.about-tabs .tab-btn.active {
    color: var(--neon-green);
    border-bottom-color: var(--neon-green);
}

.about-section {
    display: none;
}

.about-section.active {
    display: block;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group.full-width {
    grid-column: span 2;
}

.services-list,
.testimonials-list {
    display: grid;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.service-card,
.testimonial-card {
    background: var(--eerie-black-2);
    border: 1px solid var(--jet);
    border-radius: 12px;
    padding: 1.5rem;
    transition: var(--transition-1);
}

.service-card:hover,
.testimonial-card:hover {
    border-color: var(--neon-green);
    transform: translateY(-2px);
}

.service-header,
.testimonial-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.service-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    background: var(--bg-gradient-neon-green-2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.service-icon img {
    width: 24px;
    height: 24px;
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    flex-shrink: 0;
}

.service-info h4,
.testimonial-info h4 {
    color: var(--white-2);
    font-size: var(--fs-5);
    margin-bottom: 0.5rem;
}

.service-description,
.testimonial-text {
    color: var(--light-gray);
    font-size: var(--fs-6);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.testimonial-text {
    font-style: italic;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Resume Section Styles */
.education-list,
.experience-list,
.skills-list {
    display: grid;
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.education-item,
.experience-item,
.skill-item {
    background: var(--eerie-black-2);
    border: 1px solid var(--jet);
    border-radius: 12px;
    padding: 1.5rem;
    transition: var(--transition-1);
}

.education-item:hover,
.experience-item:hover,
.skill-item:hover {
    border-color: var(--neon-green);
    transform: translateY(-2px);
}

.education-header,
.experience-header,
.skill-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1rem;
}

.education-info,
.experience-info,
.skill-info {
    flex: 1;
}

.education-info h4,
.experience-info h4,
.skill-info h4 {
    color: var(--white-2);
    font-size: var(--fs-5);
    margin-bottom: 0.5rem;
}

.education-institution,
.experience-company,
.education-degree,
.experience-position {
    color: var(--light-gray);
    font-size: var(--fs-6);
    margin-bottom: 0.25rem;
}

.education-dates,
.experience-dates {
    color: var(--neon-green);
    font-size: var(--fs-7);
    font-weight: 500;
}

.education-description,
.experience-description {
    color: var(--light-gray);
    font-size: var(--fs-6);
    line-height: 1.6;
    margin-top: 1rem;
}

.skill-proficiency {
    margin-top: 0.5rem;
}

.proficiency-bar {
    width: 100%;
    height: 8px;
    background: var(--jet);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.proficiency-fill {
    height: 100%;
    background: var(--bg-gradient-neon-green-2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.proficiency-text {
    color: var(--neon-green);
    font-size: var(--fs-7);
    font-weight: 500;
}

.education-actions,
.experience-actions,
.skill-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--light-gray);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-icon:hover {
    background: var(--jet);
    color: var(--white-2);
}

.btn-icon ion-icon {
    font-size: 1.1rem;
}

/* Responsive adjustments for About section */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-group.full-width {
        grid-column: span 1;
    }
    
    .about-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .about-tabs .tab-btn {
        padding: 0.75rem 1rem;
        font-size: var(--fs-7);
    }
}

/* Fixed layout issue with section header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header div:first-child {
    flex: 1;
}

/* Form validation styles */
.form-group input:invalid,
.form-group textarea:invalid {
    border-color: #da3633;
}

.form-group input:valid,
.form-group textarea:valid {
    border-color: #238636;
}

/* Enhanced button hover effects */
.btn-small {
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    font-family: inherit;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-small ion-icon {
    font-size: 0.875rem;
}

/* Action button improvements */
.item-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Enhanced modal animations */
.modal-overlay.active .modal {
    animation: modalSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Improved scrollbar styling */
.admin-sidebar::-webkit-scrollbar,
.modal::-webkit-scrollbar {
    width: 6px;
}

.admin-sidebar::-webkit-scrollbar-track,
.modal::-webkit-scrollbar-track {
    background: #161b22;
}

.admin-sidebar::-webkit-scrollbar-thumb,
.modal::-webkit-scrollbar-thumb {
    background: #30363d;
    border-radius: 3px;
}

.admin-sidebar::-webkit-scrollbar-thumb:hover,
.modal::-webkit-scrollbar-thumb:hover {
    background: #484f58;
}

/* Enhanced focus states for accessibility */
.nav-link:focus,
.btn-primary:focus,
.btn-secondary:focus,
.tab-btn:focus,
.action-btn:focus {
    outline: 2px solid #238636;
    outline-offset: 2px;
}

/* Improved responsive typography */
@media (max-width: 480px) {
    .section-header h1 {
        font-size: 1.5rem;
    }
    
    .stat-card .stat-content h3 {
        font-size: 1.5rem;
    }
    
    .modal-header h3 {
        font-size: 1.125rem;
    }
}

/* Loading animation refinements */
.loading {
    min-height: 200px;
    font-size: 0.875rem;
}

/* Enhanced table-like layout for resume items */
.resume-item .item-actions {
    position: absolute;
    top: 1rem;
    right: 1rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.resume-item:hover .item-actions {
    opacity: 1;
}

/* Better spacing for form elements in modals */
.modal .form-group:last-of-type {
    margin-bottom: 0;
}

/* Enhanced card hover effects */
.gallery-item,
.portfolio-item,
.resume-item,
.message-item {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Improved chart container styling */
.chart-card canvas {
    border-radius: 4px;
    background: linear-gradient(135deg, #21262d 0%, #30363d 100%);
}

/* Better message status indicators */
.message-status {
    font-size: 0.625rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 600;
}

/* Enhanced skill progress bars */
.skill-progress {
    position: relative;
    overflow: hidden;
}

.skill-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Improved filter button styling */
.message-filters {
    flex-wrap: wrap;
}

/* Enhanced alert positioning */
.alert {
    position: relative;
    z-index: 100;
    border-left: 4px solid;
    font-weight: 500;
}

.alert-success {
    border-left-color: #238636;
}

.alert-error {
    border-left-color: #da3633;
}

.alert-warning {
    border-left-color: #ffc107;
}

/* Better form layout in settings */
.settings-form .form-group label {
    margin-bottom: 0.25rem;
}

/* Enhanced sidebar footer */
.sidebar-footer {
    position: sticky;
    bottom: 0;
    background: inherit;
}

/* Admin portfolio card sizing */
#portfolio-grid .portfolio-item {
    height: 400px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    background: linear-gradient(135deg, #161b22 0%, #1c2128 100%);
    border: 1px solid #21262d;
}

#portfolio-grid .portfolio-item .portfolio-cover {
    flex: 0 0 220px; /* fixed cover height */
    overflow: hidden;
}

#portfolio-grid .portfolio-item .portfolio-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

#portfolio-grid .portfolio-item .portfolio-item-info {
    padding: 12px;
    overflow: auto;
    flex: 1 1 auto;
}

#portfolio-grid .portfolio-item .portfolio-actions {
    display: flex;
    gap: 8px;
    margin-top: 8px;
}

/* Dropzone for file upload in modal */
.dropzone {
    border: 2px dashed #30363d;
    border-radius: 6px;
    padding: 16px;
    text-align: center;
    color: #8b949e;
    cursor: pointer;
}
.dropzone.dragover {
    background: rgba(35,134,54,0.06);
    border-color: #238636;
    color: #f0f6fc;
}