<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="./config.js"></script>
</head>
<body>
    <h1>Portfolio Database Test</h1>
    <div id="results"></div>
    
    <script>
        async function testPortfolio() {
            try {
                const { createClient } = supabase;
                const supabaseClient = createClient(CONFIG.supabase.url, CONFIG.supabase.key);
                
                console.log('Testing portfolio database connection...');
                
                // Test fetching portfolio items
                const { data: portfolioItems, error } = await supabaseClient
                    .from('portfolio_items')
                    .select('*')
                    .order('order_index');
                
                const resultsDiv = document.getElementById('results');
                
                if (error) {
                    console.error('Error:', error);
                    resultsDiv.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
                } else {
                    console.log('Portfolio items:', portfolioItems);
                    resultsDiv.innerHTML = `
                        <p style="color: green;">Success! Found ${portfolioItems.length} portfolio items.</p>
                        <pre>${JSON.stringify(portfolioItems, null, 2)}</pre>
                    `;
                }
                
                // Test inserting a sample portfolio item
                const sampleItem = {
                    title: 'Test Portfolio Item',
                    slug: 'test-portfolio-item',
                    category_slug: 'coaching',
                    summary: 'This is a test portfolio item',
                    body_html: '<p>This is a detailed description of the test portfolio item.</p>',
                    cover_image_url: 'https://via.placeholder.com/400x300',
                    published: true,
                    order_index: 0
                };
                
                console.log('Testing portfolio item insertion...');
                const { data: insertedItem, error: insertError } = await supabaseClient
                    .from('portfolio_items')
                    .insert(sampleItem)
                    .select();
                
                if (insertError) {
                    console.error('Insert error:', insertError);
                    resultsDiv.innerHTML += `<p style="color: red;">Insert Error: ${insertError.message}</p>`;
                } else {
                    console.log('Inserted item:', insertedItem);
                    resultsDiv.innerHTML += `<p style="color: green;">Successfully inserted test item!</p>`;
                }
                
            } catch (error) {
                console.error('Test error:', error);
                document.getElementById('results').innerHTML = `<p style="color: red;">Test Error: ${error.message}</p>`;
            }
        }
        
        // Run test when page loads
        window.addEventListener('load', testPortfolio);
    </script>
</body>
</html>
