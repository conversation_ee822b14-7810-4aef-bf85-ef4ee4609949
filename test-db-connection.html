<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Connection Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="./config.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>Database Connection Test</h1>
    <div id="results"></div>
    
    <button onclick="testConnection()">Test Connection</button>
    <button onclick="testPortfolioTable()">Test Portfolio Table</button>
    <button onclick="addSamplePortfolio()">Add Sample Portfolio</button>
    <button onclick="clearResults()">Clear Results</button>
    
    <script>
        const { createClient } = supabase;
        const supabaseClient = createClient(CONFIG.supabase.url, CONFIG.supabase.key);
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testConnection() {
            try {
                addResult('Testing Supabase connection...', 'info');
                
                const { data, error } = await supabaseClient
                    .from('portfolio_items')
                    .select('count', { count: 'exact', head: true });
                
                if (error) {
                    addResult(`Connection Error: ${error.message}`, 'error');
                } else {
                    addResult('✅ Connection successful!', 'success');
                    addResult(`Portfolio items count: ${data}`, 'info');
                }
            } catch (error) {
                addResult(`Test Error: ${error.message}`, 'error');
            }
        }
        
        async function testPortfolioTable() {
            try {
                addResult('Testing portfolio_items table...', 'info');
                
                const { data: portfolioItems, error } = await supabaseClient
                    .from('portfolio_items')
                    .select('*')
                    .order('created_at', { ascending: false });
                
                if (error) {
                    addResult(`Portfolio Table Error: ${error.message}`, 'error');
                } else {
                    addResult(`✅ Found ${portfolioItems.length} portfolio items`, 'success');
                    if (portfolioItems.length > 0) {
                        addResult(`<pre>${JSON.stringify(portfolioItems, null, 2)}</pre>`, 'info');
                    }
                }
            } catch (error) {
                addResult(`Test Error: ${error.message}`, 'error');
            }
        }
        
        async function addSamplePortfolio() {
            try {
                addResult('Adding sample portfolio item...', 'info');
                
                const sampleItem = {
                    title: 'Tennis Coaching Program',
                    slug: 'tennis-coaching-program',
                    category_slug: 'coaching',
                    summary: 'Comprehensive tennis coaching program for beginners to advanced players.',
                    body_html: '<p>This program includes personalized training sessions, technique analysis, and match strategy development.</p><p>Perfect for players looking to improve their game and compete at higher levels.</p>',
                    cover_image_url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop',
                    gallery_urls: [
                        'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=600&fit=crop',
                        'https://images.unsplash.com/photo-1622279457486-62dcc4a431d6?w=800&h=600&fit=crop'
                    ],
                    published: true,
                    order_index: 0,
                    metadata: {
                        duration: '12 weeks',
                        level: 'All levels',
                        price: '$150/session'
                    }
                };
                
                const { data: insertedItem, error: insertError } = await supabaseClient
                    .from('portfolio_items')
                    .insert(sampleItem)
                    .select();
                
                if (insertError) {
                    addResult(`Insert Error: ${insertError.message}`, 'error');
                } else {
                    addResult('✅ Sample portfolio item added successfully!', 'success');
                    addResult(`<pre>${JSON.stringify(insertedItem[0], null, 2)}</pre>`, 'info');
                }
            } catch (error) {
                addResult(`Test Error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run connection test on page load
        window.addEventListener('load', () => {
            testConnection();
        });
    </script>
</body>
</html>
