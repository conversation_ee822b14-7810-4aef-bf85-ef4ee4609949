<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="stylesheet" href="./assets/css/admin.css">
    <!-- Quill.js Rich Text Editor -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script type="module" src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@5.5.2/dist/ionicons/ionicons.js"></script>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <img src="./assets/images/logo.svg" alt="MoJack Admin" class="admin-logo">
                <h2>Admin Panel</h2>
            </div>

            <nav class="sidebar-nav">
                <ul class="nav-list">
                    <li class="nav-item">
                        <button class="nav-link active" data-section="dashboard">
                            <ion-icon name="home-outline"></ion-icon>
                            <span>Dashboard</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="site-settings">
                            <ion-icon name="settings-outline"></ion-icon>
                            <span>Site Settings</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="about">
                            <ion-icon name="person-outline"></ion-icon>
                            <span>About</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="resume">
                            <ion-icon name="document-text-outline"></ion-icon>
                            <span>Resume</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="portfolio">
                            <ion-icon name="briefcase-outline"></ion-icon>
                            <span>Portfolio</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="gallery">
                            <ion-icon name="images-outline"></ion-icon>
                            <span>Gallery</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="testimonials">
                            <ion-icon name="chatbubble-outline"></ion-icon>
                            <span>Testimonials</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="messages">
                            <ion-icon name="mail-outline"></ion-icon>
                            <span>Messages</span>
                            <span class="badge" id="message-count">0</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="media">
                            <ion-icon name="cloud-upload-outline"></ion-icon>
                            <span>Media</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="users">
                            <ion-icon name="people-outline"></ion-icon>
                            <span>Users</span>
                        </button>
                    </li>
                    <li class="nav-item">
                        <button class="nav-link" data-section="seo">
                            <ion-icon name="search-outline"></ion-icon>
                            <span>SEO</span>
                        </button>
                    </li>
                </ul>
            </nav>

            <div class="sidebar-footer">
                <a href="./index.html" class="back-to-site">
                    <ion-icon name="eye-outline"></ion-icon>
                    <span>View Site</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Dashboard Section -->
            <section id="dashboard" class="admin-section active">
                <div class="section-header">
                    <div>
                        <h1>Dashboard Overview</h1>
                        <p>Welcome back! Here's what's happening with your site.</p>
                    </div>
                    <div>
                        <button class="btn-primary" onclick="admin.previewSite()">
                            <ion-icon name="eye-outline"></ion-icon>
                            Preview Site
                        </button>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <ion-icon name="eye-outline"></ion-icon>
                        </div>
                        <div class="stat-content">
                            <h3 id="total-visits">1,247</h3>
                            <p>Total Visits</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <ion-icon name="mail-outline"></ion-icon>
                        </div>
                        <div class="stat-content">
                            <h3 id="new-messages">3</h3>
                            <p>New Messages</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <ion-icon name="images-outline"></ion-icon>
                        </div>
                        <div class="stat-content">
                            <h3 id="gallery-items">12</h3>
                            <p>Gallery Items</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <ion-icon name="briefcase-outline"></ion-icon>
                        </div>
                        <div class="stat-content">
                            <h3 id="portfolio-projects">8</h3>
                            <p>Portfolio Projects</p>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Grid -->
                <div class="dashboard-grid">
                    <div class="recent-activity">
                        <h3>Recent Activity</h3>
                        <ul id="activity-list" class="activity-list">
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="activity-content">
                                    <p>New message from John Smith</p>
                                    <small>2 hours ago</small>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <ion-icon name="images-outline"></ion-icon>
                                </div>
                                <div class="activity-content">
                                    <p>Added new gallery item</p>
                                    <small>1 day ago</small>
                                </div>
                            </li>
                            <li class="activity-item">
                                <div class="activity-icon">
                                    <ion-icon name="briefcase-outline"></ion-icon>
                                </div>
                                <div class="activity-content">
                                    <p>Updated portfolio project</p>
                                    <small>2 days ago</small>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <div class="quick-actions">
                        <h3>Quick Actions</h3>
                        <div class="action-buttons">
                            <button class="action-btn" data-action="add-gallery">
                                <ion-icon name="images-outline"></ion-icon>
                                <span>Add Gallery Item</span>
                            </button>
                            <button class="action-btn" data-action="add-portfolio">
                                <ion-icon name="briefcase-outline"></ion-icon>
                                <span>Add Portfolio Project</span>
                            </button>
                            <button class="action-btn" data-action="view-messages">
                                <ion-icon name="mail-outline"></ion-icon>
                                <span>View Messages</span>
                            </button>
                            <button class="action-btn" data-action="edit-about">
                                <ion-icon name="person-outline"></ion-icon>
                                <span>Edit About</span>
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Site Settings Section -->
            <section id="site-settings" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Site Settings</h1>
                        <p>Configure global site settings and appearance.</p>
                    </div>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>General Settings</h3>
                        <form id="site-info-form" class="settings-form">
                            <div class="form-group">
                                <label for="site-title">Site Title</label>
                                <input type="text" id="site-title" name="site-title" placeholder="MoJack - Tennis Coach">
                            </div>
                            <div class="form-group">
                                <label for="site-description">Site Description</label>
                                <textarea id="site-description" name="site-description" rows="3" placeholder="Professional tennis coaching services..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="logo-url">Logo URL</label>
                                <input type="url" id="logo-url" name="logo-url" placeholder="https://...">
                            </div>
                            <div class="form-group">
                                <label for="favicon-url">Favicon URL</label>
                                <input type="url" id="favicon-url" name="favicon-url" placeholder="https://...">
                            </div>
                            <button type="submit" class="btn-primary">Save Settings</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>Contact Information</h3>
                        <form id="contact-info-form" class="settings-form">
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" placeholder="<EMAIL>">
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" placeholder="+27 12 345 6789">
                            </div>
                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea id="address" name="address" rows="3" placeholder="Full address..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="whatsapp-link">WhatsApp Link</label>
                                <input type="url" id="whatsapp-link" name="whatsapp-link" placeholder="https://wa.me/...">
                            </div>
                            <button type="submit" class="btn-primary">Save Contact Info</button>
                        </form>
                    </div>
                </div>
            </section>

            <!-- About Section -->
            <section id="about" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>About Management</h1>
                        <p>Edit your about section content and services.</p>
                    </div>
                    <div>
                        <button class="btn-secondary" id="preview-about-btn">
                            <ion-icon name="eye-outline"></ion-icon>
                            Preview
                        </button>
                    </div>
                </div>

                <div class="about-management">
                    <div class="about-tabs">
                        <button class="tab-btn active" data-tab="personal">Personal Info</button>
                        <button class="tab-btn" data-tab="content">About Content</button>
                        <button class="tab-btn" data-tab="services">Services</button>
                        <button class="tab-btn" data-tab="testimonials">Testimonials</button>
                    </div>

                    <div id="personal-section" class="about-section active">
                        <form id="personal-info-form" class="settings-form">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label for="personal-name">Full Name</label>
                                    <input type="text" id="personal-name" name="name" placeholder="Moeketsi Paul-Jean Nolonolo">
                                </div>
                                <div class="form-group">
                                    <label for="personal-title">Professional Title</label>
                                    <input type="text" id="personal-title" name="title" placeholder="Tennis Coach">
                                </div>
                                <div class="form-group">
                                    <label for="personal-email">Email</label>
                                    <input type="email" id="personal-email" name="email" placeholder="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="personal-phone">Phone</label>
                                    <input type="tel" id="personal-phone" name="phone" placeholder="+27 78 901 3952">
                                </div>
                                <div class="form-group">
                                    <label for="personal-birthday">Birthday</label>
                                    <input type="date" id="personal-birthday" name="birthday">
                                </div>
                                <div class="form-group">
                                    <label for="personal-location">Location</label>
                                    <input type="text" id="personal-location" name="location" placeholder="Pietermaritzburg, South Africa">
                                </div>
                            </div>
                            <div class="form-group full-width">
                                <label for="personal-avatar">Avatar URL</label>
                                <input type="url" id="personal-avatar" name="avatar" placeholder="https://...">
                            </div>
                            <button type="button" class="btn-primary" id="save-personal-info">Save Personal Info</button>
                        </form>
                    </div>

                    <div id="content-section" class="about-section">
                        <form id="about-text-form" class="settings-form">
                            <div class="form-group">
                                <label for="paragraph1">Introduction Paragraph 1</label>
                                <textarea id="paragraph1" name="paragraph1" rows="4" placeholder="Your introduction text..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="paragraph2">Introduction Paragraph 2</label>
                                <textarea id="paragraph2" name="paragraph2" rows="4" placeholder="Additional introduction text..."></textarea>
                            </div>
                            <div class="form-group">
                                <label for="paragraph3">Introduction Paragraph 3 (Optional)</label>
                                <textarea id="paragraph3" name="paragraph3" rows="4" placeholder="Optional additional text..."></textarea>
                            </div>
                            <button type="button" class="btn-primary" id="save-about-text">Save About Text</button>
                        </form>

                        <div id="about-preview" class="about-preview-content" style="display: none;">
                            <h4>Preview:</h4>
                            <div id="preview-content"></div>
                        </div>
                    </div>

                    <div id="services-section" class="about-section">
                        <div class="section-header">
                            <div>
                                <h3>Services</h3>
                                <p>Manage your coaching services</p>
                            </div>
                            <button class="btn-primary" id="add-service-btn">
                                <ion-icon name="add-outline"></ion-icon>
                                Add Service
                            </button>
                        </div>
                        <div id="services-list" class="services-list">
                            <!-- Services will be loaded here -->
                        </div>
                    </div>

                    <div id="testimonials-section" class="about-section">
                        <div class="section-header">
                            <div>
                                <h3>Testimonials</h3>
                                <p>Manage client testimonials</p>
                            </div>
                            <button class="btn-primary" id="add-testimonial-btn">
                                <ion-icon name="add-outline"></ion-icon>
                                Add Testimonial
                            </button>
                        </div>
                        <div id="testimonials-list" class="testimonials-list">
                            <!-- Testimonials will be loaded here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Resume Section -->
            <section id="resume" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Resume Management</h1>
                        <p>Manage your education, experience, and skills.</p>
                    </div>
                </div>

                <div class="resume-tabs">
                    <button class="tab-btn active" data-tab="education">Education</button>
                    <button class="tab-btn" data-tab="experience">Experience</button>
                    <button class="tab-btn" data-tab="skills">Skills</button>
                </div>

                <div id="education-section" class="resume-section active">
                    <div class="section-header">
                        <div>
                            <h3>Education</h3>
                            <p>Your educational background</p>
                        </div>
                        <button class="btn-primary" id="add-education-btn">
                            <ion-icon name="add-outline"></ion-icon>
                            Add Education
                        </button>
                    </div>
                    <div id="education-list" class="education-list">
                        <!-- Education items will be loaded here -->
                    </div>
                    <div id="education-empty" class="empty-state" style="display: none;">
                        <ion-icon name="school-outline"></ion-icon>
                        <p>No education entries yet. Add your first education item.</p>
                    </div>
                </div>

                <div id="experience-section" class="resume-section">
                    <div class="section-header">
                        <div>
                            <h3>Experience</h3>
                            <p>Your professional experience</p>
                        </div>
                        <button class="btn-primary" id="add-experience-btn">
                            <ion-icon name="add-outline"></ion-icon>
                            Add Experience
                        </button>
                    </div>
                    <div id="experience-list" class="experience-list">
                        <!-- Experience items will be loaded here -->
                    </div>
                    <div id="experience-empty" class="empty-state" style="display: none;">
                        <ion-icon name="briefcase-outline"></ion-icon>
                        <p>No experience entries yet. Add your first experience item.</p>
                    </div>
                </div>

                <div id="skills-section" class="resume-section">
                    <div class="section-header">
                        <div>
                            <h3>Skills</h3>
                            <p>Your coaching skills and proficiency levels</p>
                        </div>
                        <div>
                            <button class="btn-secondary" id="reorder-skills-btn">
                                <ion-icon name="reorder-three-outline"></ion-icon>
                                Reorder Skills
                            </button>
                            <button class="btn-primary" id="add-skill-btn">
                                <ion-icon name="add-outline"></ion-icon>
                                Add Skill
                            </button>
                        </div>
                    </div>
                    <div id="skills-list" class="skills-list">
                        <!-- Skills will be loaded here -->
                    </div>
                    <div id="skills-empty" class="empty-state" style="display: none;">
                        <ion-icon name="bar-chart-outline"></ion-icon>
                        <p>No skills added yet. Add your first skill.</p>
                    </div>
                </div>
            </section>

            <!-- Portfolio Section -->
            <section id="portfolio" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Portfolio Management</h1>
                        <p>Manage your coaching programs and services.</p>
                    </div>
                    <button class="btn-primary" id="add-portfolio-btn">
                        <ion-icon name="add-outline"></ion-icon>
                        Add Project
                    </button>
                </div>

                <div class="content-tabs">
                    <button class="tab-btn active" data-tab="items">Portfolio Items</button>
                    <button class="tab-btn" data-tab="categories">Categories</button>
                </div>

                <div id="portfolio-items-section" class="tab-content active">
                    <div id="portfolio-grid" class="portfolio-grid">
                        <!-- Portfolio items will be loaded here -->
                    </div>
                </div>

                <div id="portfolio-categories-section" class="tab-content">
                    <div class="section-header">
                        <div>
                            <h3>Categories</h3>
                            <p>Manage portfolio categories</p>
                        </div>
                        <button class="btn-primary" id="add-category-btn">
                            <ion-icon name="add-outline"></ion-icon>
                            Add Category
                        </button>
                    </div>
                    <div id="categories-list" class="categories-list">
                        <!-- Categories will be loaded here -->
                    </div>
                </div>
            </section>

            <!-- Gallery Section -->
            <section id="gallery" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Gallery Management</h1>
                        <p>Manage your photos and videos.</p>
                    </div>
                    <button class="btn-primary" id="add-gallery-btn">
                        <ion-icon name="add-outline"></ion-icon>
                        Add Item
                    </button>
                </div>

                <div class="content-tabs">
                    <button class="tab-btn active" data-tab="videos">Videos</button>
                    <button class="tab-btn" data-tab="images">Images</button>
                </div>

                <div id="gallery-grid" class="gallery-grid">
                    <!-- Gallery items will be loaded here -->
                </div>
            </section>

            <!-- Testimonials Section -->
            <section id="testimonials" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Testimonials</h1>
                        <p>Manage client testimonials.</p>
                    </div>
                    <button class="btn-primary" id="add-testimonial-btn">
                        <ion-icon name="add-outline"></ion-icon>
                        Add Testimonial
                    </button>
                </div>

                <div id="testimonials-grid" class="testimonials-grid">
                    <!-- Testimonials will be loaded here -->
                </div>
            </section>

            <!-- Messages Section -->
            <section id="messages" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Contact Messages</h1>
                        <p>View and manage messages from your contact form.</p>
                    </div>
                </div>

                <div class="message-filters">
                    <button class="filter-btn active" data-filter="all">All Messages</button>
                    <button class="filter-btn" data-filter="unread">Unread</button>
                    <button class="filter-btn" data-filter="replied">Replied</button>
                </div>

                <div id="messages-list" class="messages-list">
                    <!-- Messages will be loaded here -->
                </div>
            </section>

            <!-- Media Section -->
            <section id="media" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>Media Library</h1>
                        <p>Manage your uploaded media files.</p>
                    </div>
                    <button class="btn-primary" id="upload-media-btn">
                        <ion-icon name="cloud-upload-outline"></ion-icon>
                        Upload Media
                    </button>
                </div>

                <div class="media-filters">
                    <button class="filter-btn active" data-filter="all">All Media</button>
                    <button class="filter-btn" data-filter="images">Images</button>
                    <button class="filter-btn" data-filter="videos">Videos</button>
                    <button class="filter-btn" data-filter="documents">Documents</button>
                </div>

                <div id="media-grid" class="media-grid">
                    <!-- Media items will be loaded here -->
                </div>
            </section>

            <!-- Users Section -->
            <section id="users" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>User Management</h1>
                        <p>Manage admin and editor users.</p>
                    </div>
                    <button class="btn-primary" id="add-user-btn">
                        <ion-icon name="person-add-outline"></ion-icon>
                        Add User
                    </button>
                </div>

                <div id="users-list" class="users-list">
                    <!-- Users will be loaded here -->
                </div>
            </section>

            <!-- SEO Section -->
            <section id="seo" class="admin-section">
                <div class="section-header">
                    <div>
                        <h1>SEO Settings</h1>
                        <p>Optimize your site for search engines.</p>
                    </div>
                </div>

                <div class="settings-grid">
                    <div class="settings-card">
                        <h3>Meta Tags</h3>
                        <form id="seo-form" class="settings-form">
                            <div class="form-group">
                                <label for="meta-title">Page Title</label>
                                <input type="text" id="meta-title" name="meta-title" placeholder="MoJack - Tennis Coach">
                                <small class="form-hint">Recommended: 50-60 characters</small>
                            </div>
                            <div class="form-group">
                                <label for="meta-description">Meta Description</label>
                                <textarea id="meta-description" name="meta-description" rows="3" placeholder="Professional tennis coaching services..."></textarea>
                                <small class="form-hint">Recommended: 150-160 characters</small>
                            </div>
                            <div class="form-group">
                                <label for="meta-keywords">Keywords</label>
                                <input type="text" id="meta-keywords" name="meta-keywords" placeholder="tennis coach, lessons, training">
                            </div>
                            <button type="submit" class="btn-primary">Save SEO Settings</button>
                        </form>
                    </div>

                    <div class="settings-card">
                        <h3>Analytics & Tracking</h3>
                        <form id="analytics-form" class="settings-form">
                            <div class="form-group">
                                <label for="google-analytics">Google Analytics ID</label>
                                <input type="text" id="google-analytics" name="google-analytics" placeholder="GA-XXXXXXXXXX">
                            </div>
                            <div class="form-group">
                                <label for="facebook-pixel">Facebook Pixel ID</label>
                                <input type="text" id="facebook-pixel" name="facebook-pixel" placeholder="123456789012345">
                            </div>
                            <button type="submit" class="btn-primary">Save Analytics</button>
                        </form>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Modal Overlay -->
    <div id="modal-overlay" class="modal-overlay">
        <div class="modal">
            <div class="modal-header">
                <h3 id="modal-title">Add Item</h3>
                <button id="modal-close" class="modal-close">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div class="modal-content">
                <form id="item-form" class="settings-form">
                    <!-- Form fields will be dynamically added here -->
                    <div class="form-actions">
                        <button type="button" id="modal-cancel" class="btn-secondary">Cancel</button>
                        <button type="submit" class="btn-primary">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Message Modal -->
    <div id="message-modal-overlay" class="modal-overlay">
        <div class="message-modal modal">
            <div class="modal-header">
                <h3>Message Details</h3>
                <button id="message-modal-close" class="modal-close">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div class="modal-content">
                <div id="message-details" class="message-details">
                    <!-- Message details will be loaded here -->
                </div>
                <div class="reply-section">
                    <h4>Reply to Message</h4>
                    <form id="reply-form" class="settings-form">
                        <div class="form-group">
                            <label for="reply-message">Your Reply</label>
                            <textarea id="reply-message" name="reply" rows="4" placeholder="Type your reply here..." required></textarea>
                        </div>
                        <button type="submit" class="btn-primary">Send Reply</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="./config.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2.39.3/dist/umd/supabase.js"></script>
    <script src="./assets/js/admin.js"></script>
    <script src="./test-admin.js"></script>
</body>
</html>